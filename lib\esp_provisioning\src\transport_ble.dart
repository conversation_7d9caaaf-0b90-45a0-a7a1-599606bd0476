// BLE transport implementation for ESP provisioning using flutter_blue_plus
import 'dart:async';
import 'dart:typed_data';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'transport.dart';

class TransportBLE extends ProvTransport {
  final BluetoothDevice device;
  BluetoothCharacteristic? _dataInCharacteristic;
  BluetoothCharacteristic? _dataOutCharacteristic;
  StreamSubscription<List<int>>? _notificationSubscription;
  final Completer<Uint8List> _responseCompleter = Completer<Uint8List>();
  bool _isConnected = false;

  // ESP32 Provisioning Service UUIDs
  static const String PROV_SERVICE_UUID = "0000ffff-0000-1000-8000-00805f9b34fb";
  static const String PROV_SESSION_UUID = "0000ff51-0000-1000-8000-00805f9b34fb";
  static const String PROV_CONFIG_UUID = "0000ff52-0000-1000-8000-00805f9b34fb";
  static const String PROV_SCAN_UUID = "0000ff50-0000-1000-8000-00805f9b34fb";
  static const String CUSTOM_DATA_UUID = "0000ff53-0000-1000-8000-00805f9b34fb";

  TransportBLE(this.device);

  @override
  Future<bool> connect() async {
    try {
      if (_isConnected) return true;

      await device.connect(timeout: const Duration(seconds: 10));
      await _discoverCharacteristics();
      _isConnected = true;
      return true;
    } catch (e) {
      print('BLE connection failed: $e');
      return false;
    }
  }

  @override
  Future<bool> checkConnect() async {
    return _isConnected && device.isConnected;
  }

  @override
  Future<void> disconnect() async {
    try {
      await _notificationSubscription?.cancel();
      _notificationSubscription = null;
      await device.disconnect();
      _isConnected = false;
    } catch (e) {
      print('BLE disconnect error: $e');
    }
  }

  Future<void> _discoverCharacteristics() async {
    final services = await device.discoverServices();
    
    for (final service in services) {
      // Look for provisioning service
      if (service.uuid.toString().toLowerCase().contains('ffff') ||
          service.uuid.toString().toLowerCase() == PROV_SERVICE_UUID.toLowerCase()) {
        
        for (final characteristic in service.characteristics) {
          final uuid = characteristic.uuid.toString().toLowerCase();
          
          // Map characteristics based on properties
          if (characteristic.properties.write || characteristic.properties.writeWithoutResponse) {
            _dataInCharacteristic = characteristic;
          }
          
          if (characteristic.properties.notify || characteristic.properties.indicate) {
            _dataOutCharacteristic = characteristic;
            await characteristic.setNotifyValue(true);
            _notificationSubscription = characteristic.value.listen(_onDataReceived);
          }
        }
        break;
      }
    }

    if (_dataInCharacteristic == null || _dataOutCharacteristic == null) {
      throw Exception('Required provisioning characteristics not found');
    }
  }

  void _onDataReceived(List<int> data) {
    if (!_responseCompleter.isCompleted) {
      _responseCompleter.complete(Uint8List.fromList(data));
    }
  }

  @override
  Future<Uint8List> sendReceive(String path, Uint8List data) async {
    if (!_isConnected || _dataInCharacteristic == null) {
      throw Exception('BLE not connected or characteristics not available');
    }

    try {
      // Clear any previous response
      if (_responseCompleter.isCompleted) {
        _responseCompleter.complete(Uint8List(0));
      }

      // Send data
      await _dataInCharacteristic!.write(data, withoutResponse: true);

      // Wait for response with timeout
      final response = await _responseCompleter.future.timeout(
        const Duration(seconds: 10),
        onTimeout: () => throw TimeoutException('No response received', const Duration(seconds: 10)),
      );

      return response;
    } catch (e) {
      print('BLE sendReceive error: $e');
      rethrow;
    }
  }
}
