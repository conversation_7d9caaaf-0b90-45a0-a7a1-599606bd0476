// BLE transport implementation for mesh provisioning using flutter_blue_plus
import 'dart:async';
import 'dart:typed_data';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'transport.dart';

class TransportBLE extends ProvTransport {
  final BluetoothDevice device;
  BluetoothCharacteristic? _dataInCharacteristic;
  BluetoothCharacteristic? _dataOutCharacteristic;
  StreamSubscription<List<int>>? _notificationSubscription;
  final Completer<Uint8List> _responseCompleter = Completer<Uint8List>();
  bool _isConnected = false;

  // BLE Mesh Provisioning Service UUIDs
  static const String MESH_PROV_SERVICE_UUID = "00001827-0000-1000-8000-00805f9b34fb";
  static const String MESH_PROV_DATA_IN_UUID = "00002adb-0000-1000-8000-00805f9b34fb";
  static const String MESH_PROV_DATA_OUT_UUID = "00002adc-0000-1000-8000-00805f9b34fb";

  TransportBLE(this.device);

  @override
  Future<bool> connect() async {
    try {
      if (_isConnected) return true;

      await device.connect(timeout: const Duration(seconds: 10));
      await _discoverCharacteristics();
      _isConnected = true;
      return true;
    } catch (e) {
      print('BLE connection failed: $e');
      return false;
    }
  }

  @override
  Future<bool> checkConnect() async {
    return _isConnected && device.isConnected;
  }

  @override
  Future<void> disconnect() async {
    try {
      await _notificationSubscription?.cancel();
      _notificationSubscription = null;
      await device.disconnect();
      _isConnected = false;
    } catch (e) {
      print('BLE disconnect error: $e');
    }
  }

  Future<void> _discoverCharacteristics() async {
    final services = await device.discoverServices();
    
    for (final service in services) {
      // Look for mesh provisioning service
      if (service.uuid.toString().toLowerCase().contains('1827') ||
          service.uuid.toString().toLowerCase() == MESH_PROV_SERVICE_UUID.toLowerCase()) {

        for (final characteristic in service.characteristics) {
          final uuid = characteristic.uuid.toString().toLowerCase();

          // Map characteristics based on specific UUIDs
          if (uuid.contains('2adb') || uuid == MESH_PROV_DATA_IN_UUID.toLowerCase()) {
            _dataInCharacteristic = characteristic;
          }

          if (uuid.contains('2adc') || uuid == MESH_PROV_DATA_OUT_UUID.toLowerCase()) {
            _dataOutCharacteristic = characteristic;
            await characteristic.setNotifyValue(true);
            _notificationSubscription = characteristic.value.listen(_onDataReceived);
          }
        }
        break;
      }
    }

    if (_dataInCharacteristic == null || _dataOutCharacteristic == null) {
      throw Exception('Required mesh provisioning characteristics not found');
    }
  }

  void _onDataReceived(List<int> data) {
    if (!_responseCompleter.isCompleted) {
      _responseCompleter.complete(Uint8List.fromList(data));
    }
  }

  @override
  Future<Uint8List> sendReceive(String path, Uint8List data) async {
    if (!_isConnected || _dataInCharacteristic == null) {
      throw Exception('BLE not connected or characteristics not available');
    }

    try {
      // For mesh provisioning, we ignore the path parameter
      // and use the mesh provisioning characteristics directly

      // Send data to mesh provisioning data in characteristic
      await _dataInCharacteristic!.write(data, withoutResponse: false);
      print('Sent mesh provisioning data: ${data.map((e) => '0x${e.toRadixString(16).padLeft(2, '0')}').join(' ')}');

      // Wait for response from mesh provisioning data out characteristic
      final response = await _waitForResponse();
      print('Received mesh provisioning response: ${response.map((e) => '0x${e.toRadixString(16).padLeft(2, '0')}').join(' ')}');

      return response;
    } catch (e) {
      print('BLE mesh provisioning error: $e');
      rethrow;
    }
  }

  Future<Uint8List> _waitForResponse() async {
    final completer = Completer<Uint8List>();
    StreamSubscription<List<int>>? subscription;

    subscription = _dataOutCharacteristic!.value.listen((data) {
      if (!completer.isCompleted && data.isNotEmpty) {
        subscription?.cancel();
        completer.complete(Uint8List.fromList(data));
      }
    });

    // Set timeout
    Timer(const Duration(seconds: 10), () {
      if (!completer.isCompleted) {
        subscription?.cancel();
        completer.completeError(TimeoutException('No response received', const Duration(seconds: 10)));
      }
    });

    return completer.future;
  }
}
