// Main ESP provisioning class
import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/widgets.dart';
import 'connection_models.dart';
import 'protos/generated/constants.pbenum.dart';
import 'protos/generated/session.pb.dart';
import 'protos/generated/wifi_config.pb.dart';
import 'protos/generated/wifi_scan.pb.dart';
import 'security.dart';
import 'transport.dart';

class EspProv {
  final ProvTransport transport;
  final ProvSecurity security;

  EspProv({required this.transport, required this.security});

  Future<EstablishSessionStatus> establishSession() async {
    try {
      SessionData? responseData;
      
      // Connect to device
      if (!await transport.connect()) {
        return EstablishSessionStatus.disconnected;
      }

      while (await transport.checkConnect()) {
        var request = await security.securitySession(responseData);
        if (request == null) {
          return EstablishSessionStatus.connected;
        }

        var response = await transport.sendReceive(
            'prov-session', request.writeToBuffer());
        if (response.isEmpty) {
          throw Exception('Empty response');
        }

        responseData = SessionData.fromBuffer(response);
      }

      return EstablishSessionStatus.disconnected;
    } catch (e) {
      if (await transport.checkConnect()) {
        return EstablishSessionStatus.keymismatch;
      } else {
        debugPrint('-----------------------');
        debugPrint('EstablishSession Error:');
        debugPrint('$e');
        debugPrint('-----------------------');
        return EstablishSessionStatus.disconnected;
      }
    }
  }

  Future<void> dispose() async {
    await transport.disconnect();
  }

  Future<List<WifiAP>> startScanWiFi() async {
    return await scan();
  }

  Future<WiFiScanPayload> startScanResponse(Uint8List data) async {
    var respPayload = WiFiScanPayload.fromBuffer(await security.decrypt(data));
    if (respPayload.msg != WiFiScanMsgType.TypeRespScanStart) {
      throw Exception('Invalid expected message type $respPayload');
    }
    return respPayload;
  }

  Future<WiFiScanPayload> startScanRequest({
    bool blocking = true,
    bool passive = false,
    int groupChannels = 5,
    int periodMs = 0,
  }) async {
    WiFiScanPayload payload = WiFiScanPayload();
    payload.msg = WiFiScanMsgType.TypeCmdScanStart;
    CmdScanStart scanStart = CmdScanStart();
    scanStart.blocking = blocking;
    scanStart.passive = passive;
    scanStart.groupChannels = groupChannels;
    scanStart.periodMs = periodMs;
    payload.cmdScanStart = scanStart;

    var reqData = await security.encrypt(payload.writeToBuffer());
    var respData = await transport.sendReceive('prov-scan', reqData);
    return await startScanResponse(respData);
  }

  Future<WiFiScanPayload> scanStatusResponse(Uint8List data) async {
    var respPayload = WiFiScanPayload.fromBuffer(await security.decrypt(data));
    if (respPayload.msg != WiFiScanMsgType.TypeRespScanStatus) {
      throw Exception('Invalid expected message type $respPayload');
    }
    return respPayload;
  }

  Future<WiFiScanPayload> scanStatusRequest() async {
    WiFiScanPayload payload = WiFiScanPayload();
    payload.msg = WiFiScanMsgType.TypeCmdScanStatus;

    var reqData = await security.encrypt(payload.writeToBuffer());
    var respData = await transport.sendReceive('prov-scan', reqData);
    return await scanStatusResponse(respData);
  }

  Future<List<WifiAP>> scanResultRequest({
    int startIndex = 0,
    int count = 0,
  }) async {
    WiFiScanPayload payload = WiFiScanPayload();
    payload.msg = WiFiScanMsgType.TypeCmdScanResult;
    CmdScanResult cmdScanResult = CmdScanResult();
    cmdScanResult.startIndex = startIndex;
    cmdScanResult.count = count;
    payload.cmdScanResult = cmdScanResult;

    var reqData = await security.encrypt(payload.writeToBuffer());
    var respData = await transport.sendReceive('prov-scan', reqData);
    return await scanResultResponse(respData);
  }

  Future<List<WifiAP>> scanResultResponse(Uint8List data) async {
    var respPayload = WiFiScanPayload.fromBuffer(await security.decrypt(data));
    if (respPayload.msg != WiFiScanMsgType.TypeRespScanResult) {
      throw Exception('Invalid expected message type $respPayload');
    }

    List<WifiAP> ret = [];
    for (var entry in respPayload.respScanResult.entries) {
      ret.add(WifiAP(
        ssid: utf8.decode(entry.ssid),
        rssi: entry.rssi,
        private: entry.auth.toString() != 'Open',
      ));
    }
    return ret;
  }

  Future<List<WifiAP>> scan({
    bool blocking = true,
    bool passive = false,
    int groupChannels = 5,
    int periodMs = 0,
  }) async {
    await startScanRequest(
      blocking: blocking,
      passive: passive,
      groupChannels: groupChannels,
      periodMs: periodMs,
    );

    var status = await scanStatusRequest();
    var resultCount = status.respScanStatus.resultCount;

    List<WifiAP> ret = [];
    if (resultCount > 0) {
      var index = 0;
      var remaining = resultCount;
      while (remaining > 0) {
        var count = remaining > 4 ? 4 : remaining;
        var data = await scanResultRequest(startIndex: index, count: count);
        ret.addAll(data);
        remaining -= count;
        index += count;
      }
    }
    return ret;
  }

  Future<bool> sendWifiConfig({
    required String ssid,
    required String password,
  }) async {
    var payload = WiFiConfigPayload();
    payload.msg = WiFiConfigMsgType.TypeCmdSetConfig;
    var cmdSetConfig = CmdSetConfig();
    cmdSetConfig.ssid = utf8.encode(ssid);
    cmdSetConfig.passphrase = utf8.encode(password);
    payload.cmdSetConfig = cmdSetConfig;

    var reqData = await security.encrypt(payload.writeToBuffer());
    var respData = await transport.sendReceive('prov-config', reqData);
    var respRaw = await security.decrypt(respData);
    var respPayload = WiFiConfigPayload.fromBuffer(respRaw);

    return (respPayload.respSetConfig.status == Status.Success);
  }

  Future<bool> applyWifiConfig() async {
    var payload = WiFiConfigPayload();
    payload.msg = WiFiConfigMsgType.TypeCmdApplyConfig;

    var reqData = await security.encrypt(payload.writeToBuffer());
    var respData = await transport.sendReceive('prov-config', reqData);
    var respRaw = await security.decrypt(respData);
    var respPayload = WiFiConfigPayload.fromBuffer(respRaw);

    return (respPayload.respApplyConfig.status == Status.Success);
  }

  Future<ConnectionStatus> getStatus() async {
    var payload = WiFiConfigPayload();
    payload.msg = WiFiConfigMsgType.TypeCmdGetStatus;
    var cmdGetStatus = CmdGetStatus();
    payload.cmdGetStatus = cmdGetStatus;

    var reqData = await security.encrypt(payload.writeToBuffer());
    var respData = await transport.sendReceive('prov-config', reqData);
    var respRaw = await security.decrypt(respData);
    var respPayload = WiFiConfigPayload.fromBuffer(respRaw);

    if (respPayload.respGetStatus.staState.value == 0) {
      return ConnectionStatus(
        state: WifiConnectionState.connected,
        deviceIp: respPayload.respGetStatus.connected.ip4Addr,
      );
    } else if (respPayload.respGetStatus.staState.value == 1) {
      return ConnectionStatus(state: WifiConnectionState.connecting);
    } else if (respPayload.respGetStatus.staState.value == 2) {
      return ConnectionStatus(state: WifiConnectionState.disconnected);
    } else if (respPayload.respGetStatus.staState.value == 3) {
      if (respPayload.respGetStatus.failReason.value == 0) {
        return ConnectionStatus(
          state: WifiConnectionState.connectionFailed,
          failedReason: WifiConnectFailedReason.authError,
        );
      } else if (respPayload.respGetStatus.failReason.value == 1) {
        return ConnectionStatus(
          state: WifiConnectionState.connectionFailed,
          failedReason: WifiConnectFailedReason.networkNotFound,
        );
      }
      return ConnectionStatus(state: WifiConnectionState.connectionFailed);
    }

    return ConnectionStatus(
      state: WifiConnectionState.connectionFailed,
      failedReason: WifiConnectFailedReason.authError,
    );
  }

  Future<Uint8List> sendReceiveCustomData(Uint8List data,
      {int packageSize = 256}) async {
    var i = data.length;
    var offset = 0;
    List<int> ret = [];

    while (i > 0) {
      var needToSend = data.sublist(offset, i < packageSize ? i : packageSize);
      var encrypted = await security.encrypt(needToSend);
      var newData = await transport.sendReceive('custom-data', encrypted);
      if (newData.isNotEmpty) {
        var decrypted = await security.decrypt(newData);
        ret += List.from(decrypted);
      }
      i -= packageSize;
    }
    return Uint8List.fromList(ret);
  }
}
