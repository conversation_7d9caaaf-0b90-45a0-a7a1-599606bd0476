// Generated protobuf session messages
import 'dart:core' as $core;
import 'dart:convert' as $convert;
import 'dart:typed_data' as $typed_data;
import 'package:protobuf/protobuf.dart' as $pb;
import 'constants.pbenum.dart' as $0;

class SessionData extends $pb.GeneratedMessage {
  factory SessionData({
    SecSchemeVersion? secVer,
    $core.List<$core.int>? sec0,
    Sec1Payload? sec1,
  }) {
    final $result = create();
    if (secVer != null) {
      $result.secVer = secVer;
    }
    if (sec0 != null) {
      $result.sec0 = sec0;
    }
    if (sec1 != null) {
      $result.sec1 = sec1;
    }
    return $result;
  }
  SessionData._() : super();
  factory SessionData.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SessionData.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'SessionData', createEmptyInstance: create)
    ..e<SecSchemeVersion>(1, _omitFieldNames ? '' : 'secVer', $pb.PbFieldType.OE, defaultOrMaker: SecSchemeVersion.SecScheme0, valueOf: SecSchemeVersion.valueOf, enumValues: SecSchemeVersion.values)
    ..a<$core.List<$core.int>>(2, _omitFieldNames ? '' : 'sec0', $pb.PbFieldType.OY)
    ..aOM<Sec1Payload>(3, _omitFieldNames ? '' : 'sec1', subBuilder: Sec1Payload.create)
    ..hasRequiredFields = false;

  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
      'Will be removed in next major version')
  SessionData clone() => SessionData()..mergeFromMessage(this);
  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
      'Will be removed in next major version')
  SessionData copyWith(void Function(SessionData) updates) => super.copyWith((message) => updates(message as SessionData)) as SessionData;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static SessionData create() => SessionData._();
  SessionData createEmptyInstance() => create();
  static $pb.PbList<SessionData> createRepeated() => $pb.PbList<SessionData>();
  @$core.pragma('dart2js:noInline')
  static SessionData getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SessionData>(create);
  static SessionData? _defaultInstance;

  SessionData_Payload whichPayload() => _SessionData_PayloadByTag[$_whichOneof(0)]!;
  void clearPayload() => clearField($_whichOneof(0));

  @$pb.TagNumber(1)
  SecSchemeVersion get secVer => $_getN(0);
  @$pb.TagNumber(1)
  set secVer(SecSchemeVersion v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasSecVer() => $_has(0);
  @$pb.TagNumber(1)
  void clearSecVer() => clearField(1);

  @$pb.TagNumber(2)
  $core.List<$core.int> get sec0 => $_getN(1);
  @$pb.TagNumber(2)
  set sec0($core.List<$core.int> v) { $_setBytes(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasSec0() => $_has(1);
  @$pb.TagNumber(2)
  void clearSec0() => clearField(2);

  @$pb.TagNumber(3)
  Sec1Payload get sec1 => $_getN(2);
  @$pb.TagNumber(3)
  set sec1(Sec1Payload v) { setField(3, v); }
  @$pb.TagNumber(3)
  $core.bool hasSec1() => $_has(2);
  @$pb.TagNumber(3)
  void clearSec1() => clearField(3);
  @$pb.TagNumber(3)
  Sec1Payload ensureSec1() => $_ensure(2);
}

enum SessionData_Payload {
  sec0, 
  sec1, 
  notSet
}

const _SessionData_PayloadByTag = {
  2 : SessionData_Payload.sec0,
  3 : SessionData_Payload.sec1,
  0 : SessionData_Payload.notSet
};

class Sec1Payload extends $pb.GeneratedMessage {
  factory Sec1Payload({
    Sec1MsgType? msg,
    SessionCmd? sc,
    SessionResp? sr,
  }) {
    final $result = create();
    if (msg != null) {
      $result.msg = msg;
    }
    if (sc != null) {
      $result.sc = sc;
    }
    if (sr != null) {
      $result.sr = sr;
    }
    return $result;
  }
  Sec1Payload._() : super();
  factory Sec1Payload.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory Sec1Payload.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'Sec1Payload', createEmptyInstance: create)
    ..e<Sec1MsgType>(1, _omitFieldNames ? '' : 'msg', $pb.PbFieldType.OE, defaultOrMaker: Sec1MsgType.Session_Command, valueOf: Sec1MsgType.valueOf, enumValues: Sec1MsgType.values)
    ..aOM<SessionCmd>(20, _omitFieldNames ? '' : 'sc', subBuilder: SessionCmd.create)
    ..aOM<SessionResp>(21, _omitFieldNames ? '' : 'sr', subBuilder: SessionResp.create)
    ..hasRequiredFields = false;

  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
      'Will be removed in next major version')
  Sec1Payload clone() => Sec1Payload()..mergeFromMessage(this);
  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
      'Will be removed in next major version')
  Sec1Payload copyWith(void Function(Sec1Payload) updates) => super.copyWith((message) => updates(message as Sec1Payload)) as Sec1Payload;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static Sec1Payload create() => Sec1Payload._();
  Sec1Payload createEmptyInstance() => create();
  static $pb.PbList<Sec1Payload> createRepeated() => $pb.PbList<Sec1Payload>();
  @$core.pragma('dart2js:noInline')
  static Sec1Payload getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<Sec1Payload>(create);
  static Sec1Payload? _defaultInstance;

  Sec1Payload_Payload whichPayload() => _Sec1Payload_PayloadByTag[$_whichOneof(0)]!;
  void clearPayload() => clearField($_whichOneof(0));

  @$pb.TagNumber(1)
  Sec1MsgType get msg => $_getN(0);
  @$pb.TagNumber(1)
  set msg(Sec1MsgType v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasMsg() => $_has(0);
  @$pb.TagNumber(1)
  void clearMsg() => clearField(1);

  @$pb.TagNumber(20)
  SessionCmd get sc => $_getN(1);
  @$pb.TagNumber(20)
  set sc(SessionCmd v) { setField(20, v); }
  @$pb.TagNumber(20)
  $core.bool hasSc() => $_has(1);
  @$pb.TagNumber(20)
  void clearSc() => clearField(20);
  @$pb.TagNumber(20)
  SessionCmd ensureSc() => $_ensure(1);

  @$pb.TagNumber(21)
  SessionResp get sr => $_getN(2);
  @$pb.TagNumber(21)
  set sr(SessionResp v) { setField(21, v); }
  @$pb.TagNumber(21)
  $core.bool hasSr() => $_has(2);
  @$pb.TagNumber(21)
  void clearSr() => clearField(21);
  @$pb.TagNumber(21)
  SessionResp ensureSr() => $_ensure(2);
}

enum Sec1Payload_Payload {
  sc, 
  sr, 
  notSet
}

const _Sec1Payload_PayloadByTag = {
  20 : Sec1Payload_Payload.sc,
  21 : Sec1Payload_Payload.sr,
  0 : Sec1Payload_Payload.notSet
};

class SessionCmd extends $pb.GeneratedMessage {
  factory SessionCmd({
    $core.List<$core.int>? clientVerifyData,
  }) {
    final $result = create();
    if (clientVerifyData != null) {
      $result.clientVerifyData = clientVerifyData;
    }
    return $result;
  }
  SessionCmd._() : super();
  factory SessionCmd.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SessionCmd.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'SessionCmd', createEmptyInstance: create)
    ..a<$core.List<$core.int>>(1, _omitFieldNames ? '' : 'clientVerifyData', $pb.PbFieldType.OY)
    ..hasRequiredFields = false;

  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
      'Will be removed in next major version')
  SessionCmd clone() => SessionCmd()..mergeFromMessage(this);
  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
      'Will be removed in next major version')
  SessionCmd copyWith(void Function(SessionCmd) updates) => super.copyWith((message) => updates(message as SessionCmd)) as SessionCmd;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static SessionCmd create() => SessionCmd._();
  SessionCmd createEmptyInstance() => create();
  static $pb.PbList<SessionCmd> createRepeated() => $pb.PbList<SessionCmd>();
  @$core.pragma('dart2js:noInline')
  static SessionCmd getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SessionCmd>(create);
  static SessionCmd? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$core.int> get clientVerifyData => $_getN(0);
  @$pb.TagNumber(1)
  set clientVerifyData($core.List<$core.int> v) { $_setBytes(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasClientVerifyData() => $_has(0);
  @$pb.TagNumber(1)
  void clearClientVerifyData() => clearField(1);
}

class SessionResp extends $pb.GeneratedMessage {
  factory SessionResp({
    Status? status,
    $core.List<$core.int>? deviceVerifyData,
  }) {
    final $result = create();
    if (status != null) {
      $result.status = status;
    }
    if (deviceVerifyData != null) {
      $result.deviceVerifyData = deviceVerifyData;
    }
    return $result;
  }
  SessionResp._() : super();
  factory SessionResp.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SessionResp.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'SessionResp', createEmptyInstance: create)
    ..e<Status>(1, _omitFieldNames ? '' : 'status', $pb.PbFieldType.OE, defaultOrMaker: Status.Success, valueOf: Status.valueOf, enumValues: Status.values)
    ..a<$core.List<$core.int>>(2, _omitFieldNames ? '' : 'deviceVerifyData', $pb.PbFieldType.OY)
    ..hasRequiredFields = false;

  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
      'Will be removed in next major version')
  SessionResp clone() => SessionResp()..mergeFromMessage(this);
  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
      'Will be removed in next major version')
  SessionResp copyWith(void Function(SessionResp) updates) => super.copyWith((message) => updates(message as SessionResp)) as SessionResp;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static SessionResp create() => SessionResp._();
  SessionResp createEmptyInstance() => create();
  static $pb.PbList<SessionResp> createRepeated() => $pb.PbList<SessionResp>();
  @$core.pragma('dart2js:noInline')
  static SessionResp getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SessionResp>(create);
  static SessionResp? _defaultInstance;

  @$pb.TagNumber(1)
  Status get status => $_getN(0);
  @$pb.TagNumber(1)
  set status(Status v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasStatus() => $_has(0);
  @$pb.TagNumber(1)
  void clearStatus() => clearField(1);

  @$pb.TagNumber(2)
  $core.List<$core.int> get deviceVerifyData => $_getN(1);
  @$pb.TagNumber(2)
  set deviceVerifyData($core.List<$core.int> v) { $_setBytes(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasDeviceVerifyData() => $_has(1);
  @$pb.TagNumber(2)
  void clearDeviceVerifyData() => clearField(2);
}

class Sec1MsgType extends $pb.ProtobufEnum {
  static const Sec1MsgType Session_Command = Sec1MsgType._(0, _omitEnumNames ? '' : 'Session_Command');
  static const Sec1MsgType Session_Response = Sec1MsgType._(1, _omitEnumNames ? '' : 'Session_Response');

  static const $core.List<Sec1MsgType> values = <Sec1MsgType>[
    Session_Command,
    Session_Response,
  ];

  static final $core.Map<$core.int, Sec1MsgType> _byValue = $pb.ProtobufEnum.initByValue(values);
  static Sec1MsgType? valueOf($core.int value) => _byValue[value];

  const Sec1MsgType._($core.int v, $core.String n) : super(v, n);
}

typedef SecSchemeVersion = $0.SecSchemeVersion;
typedef Status = $0.Status;

const _omitEnumNames = $core.bool.fromEnvironment('protobuf.omit_enum_names');
const _omitFieldNames = $core.bool.fromEnvironment('protobuf.omit_field_names');
const _omitMessageNames = $core.bool.fromEnvironment('protobuf.omit_message_names');
