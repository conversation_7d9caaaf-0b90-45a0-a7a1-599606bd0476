import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'esp_provisioning/esp_provisioning.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'ESP32 Provisioning',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
      ),
      home: const ESP32ProvisioningPage(),
    );
  }
}

class ESP32ProvisioningPage extends StatefulWidget {
  const ESP32ProvisioningPage({super.key});

  @override
  State<ESP32ProvisioningPage> createState() => _ESP32ProvisioningPageState();
}

class _ESP32ProvisioningPageState extends State<ESP32ProvisioningPage> {
  // ESP32 Provisioning Service UUIDs
  static const String PROV_SERVICE_UUID = "0000ffff-0000-1000-8000-00805f9b34fb";

  List<BluetoothDevice> _discoveredDevices = [];
  BluetoothDevice? _connectedDevice;
  bool _isScanning = false;
  bool _isConnected = false;
  bool _isProvisioning = false;
  String _statusMessage = "Ready to scan for devices";

  // Debug logging
  final List<String> _debugLogs = [];

  // ESP Provisioning
  EspProv? _espProv;
  List<WifiAP> _wifiNetworks = [];
  String _selectedSSID = '';
  String _wifiPassword = '';
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _popController = TextEditingController();

  // Provisioning state
  bool _sessionEstablished = false;
  bool _wifiConfigSent = false;
  bool _wifiConfigApplied = false;

  void addLog(String log) {
    setState(() {
      _debugLogs.add('[${DateTime.now().toIso8601String()}] $log');
      if (_debugLogs.length > 200) {
        _debugLogs.removeAt(0); // Limit log size
      }
    });
  }

  @override
  void initState() {
    super.initState();
    addLog('ESP32 Provisioning App initialized');
    _popController.text = 'abcd1234'; // Default PoP
    _initBLE();
  }

  @override
  void dispose() {
    _passwordController.dispose();
    _popController.dispose();
    _espProv?.dispose();
    super.dispose();
  }

  Future<void> _initBLE() async {
    addLog('Initializing BLE');
    // Request permissions
    await _requestPermissions();

    // Check if Bluetooth is available
    if (await FlutterBluePlus.isAvailable == false) {
      addLog('Bluetooth not available');
      setState(() {
        _statusMessage = "Bluetooth not available";
      });
      return;
    }

    // Turn on Bluetooth if it's off
    addLog('Turning on Bluetooth');
    await FlutterBluePlus.turnOn();
    addLog('BLE initialization completed');
  }

  Future<void> _requestPermissions() async {
    addLog('Requesting permissions');
    await [
      Permission.bluetooth,
      Permission.bluetoothScan,
      Permission.bluetoothConnect,
      Permission.location,
    ].request();
    addLog('Permissions requested');
  }

  Future<void> _startScan() async {
    if (_isScanning) return;

    addLog('Starting BLE scan for ESP32 provisioning devices');
    setState(() {
      _isScanning = true;
      _discoveredDevices.clear();
      _statusMessage = "Scanning for ESP32 devices...";
    });

    try {
      // Start scanning for devices with provisioning service
      addLog('Scanning for devices with provisioning service');
      FlutterBluePlus.startScan(
        withServices: [Guid(PROV_SERVICE_UUID)],
        timeout: const Duration(seconds: 10),
      );

      // Listen for scan results
      FlutterBluePlus.scanResults.listen((results) {
        for (ScanResult result in results) {
          if (!_discoveredDevices.contains(result.device)) {
            addLog('Found device: ${result.device.name.isEmpty ? 'Unknown' : result.device.name} (${result.device.id})');
            setState(() {
              _discoveredDevices.add(result.device);
            });
          }
        }
      });

      // Stop scanning after timeout
      await Future.delayed(const Duration(seconds: 10));
      await FlutterBluePlus.stopScan();
      addLog('Scan completed');
    } catch (e) {
      addLog('Error during scan: $e');
      setState(() {
        _statusMessage = "Error during scan: $e";
      });
    }

    setState(() {
      _isScanning = false;
      if (_discoveredDevices.isEmpty) {
        addLog('No ESP32 provisioning devices found');
        _statusMessage = "No ESP32 provisioning devices found";
      } else {
        addLog('Found ${_discoveredDevices.length} device(s)');
        _statusMessage = "Found ${_discoveredDevices.length} device(s)";
      }
    });
  }

  Future<void> _connectToDevice(BluetoothDevice device) async {
    if (_isConnected) return;

    addLog('Attempting to connect to device: ${device.name.isEmpty ? 'Unknown' : device.name} (${device.id})');
    setState(() {
      _statusMessage = "Connecting to ${device.name}...";
    });

    try {
      // Create ESP provisioning instance
      final transport = TransportBLE(device);
      final security = Security1(pop: _popController.text);
      _espProv = EspProv(transport: transport, security: security);

      addLog('Successfully connected to device');
      setState(() {
        _connectedDevice = device;
        _isConnected = true;
        _statusMessage = "Connected to ${device.name}. Ready to establish session.";
      });
    } catch (e) {
      addLog('Failed to connect: $e');
      setState(() {
        _statusMessage = "Failed to connect: $e";
      });
    }
  }

  Future<void> _establishSession() async {
    if (_espProv == null) {
      addLog('ESP provisioning not initialized');
      return;
    }

    addLog('Establishing secure session...');
    setState(() {
      _statusMessage = "Establishing secure session...";
    });

    try {
      final status = await _espProv!.establishSession();

      switch (status) {
        case EstablishSessionStatus.connected:
          addLog('Session established successfully');
          setState(() {
            _sessionEstablished = true;
            _statusMessage = "Session established. Ready to scan WiFi networks.";
          });
          break;
        case EstablishSessionStatus.disconnected:
          addLog('Failed to establish session - device disconnected');
          setState(() {
            _statusMessage = "Failed to establish session - device disconnected";
          });
          break;
        case EstablishSessionStatus.keymismatch:
          addLog('Failed to establish session - invalid PoP');
          setState(() {
            _statusMessage = "Failed to establish session - check PoP";
          });
          break;
      }
    } catch (e) {
      addLog('Error establishing session: $e');
      setState(() {
        _statusMessage = "Error establishing session: $e";
      });
    }
  }

  Future<void> _scanWiFiNetworks() async {
    if (_espProv == null || !_sessionEstablished) {
      addLog('Session not established');
      return;
    }

    addLog('Scanning for WiFi networks...');
    setState(() {
      _statusMessage = "Scanning for WiFi networks...";
    });

    try {
      final networks = await _espProv!.startScanWiFi();
      addLog('Found ${networks.length} WiFi networks');

      setState(() {
        _wifiNetworks = networks;
        _statusMessage = "Found ${networks.length} WiFi networks";
      });
    } catch (e) {
      addLog('Error scanning WiFi: $e');
      setState(() {
        _statusMessage = "Error scanning WiFi: $e";
      });
    }
  }

  Future<void> _sendWiFiConfig() async {
    if (_espProv == null || !_sessionEstablished || _selectedSSID.isEmpty) {
      addLog('Cannot send WiFi config - session not established or no SSID selected');
      return;
    }

    addLog('Sending WiFi configuration...');
    setState(() {
      _statusMessage = "Sending WiFi configuration...";
    });

    try {
      final success = await _espProv!.sendWifiConfig(
        ssid: _selectedSSID,
        password: _wifiPassword,
      );

      if (success) {
        addLog('WiFi configuration sent successfully');
        setState(() {
          _wifiConfigSent = true;
          _statusMessage = "WiFi config sent. Ready to apply.";
        });
      } else {
        addLog('Failed to send WiFi configuration');
        setState(() {
          _statusMessage = "Failed to send WiFi configuration";
        });
      }
    } catch (e) {
      addLog('Error sending WiFi config: $e');
      setState(() {
        _statusMessage = "Error sending WiFi config: $e";
      });
    }
  }

  Future<void> _applyWiFiConfig() async {
    if (_espProv == null || !_wifiConfigSent) {
      addLog('Cannot apply WiFi config - config not sent');
      return;
    }

    addLog('Applying WiFi configuration...');
    setState(() {
      _statusMessage = "Applying WiFi configuration...";
    });

    try {
      final success = await _espProv!.applyWifiConfig();

      if (success) {
        addLog('WiFi configuration applied successfully');
        setState(() {
          _wifiConfigApplied = true;
          _statusMessage = "WiFi config applied. Checking status...";
        });

        // Check status after applying
        await _checkWiFiStatus();
      } else {
        addLog('Failed to apply WiFi configuration');
        setState(() {
          _statusMessage = "Failed to apply WiFi configuration";
        });
      }
    } catch (e) {
      addLog('Error applying WiFi config: $e');
      setState(() {
        _statusMessage = "Error applying WiFi config: $e";
      });
    }
  }

  Future<void> _checkWiFiStatus() async {
    if (_espProv == null) return;

    addLog('Checking WiFi connection status...');

    try {
      final status = await _espProv!.getStatus();

      switch (status.state) {
        case WifiConnectionState.connected:
          addLog('Device connected to WiFi successfully!');
          addLog('Device IP: ${status.deviceIp ?? 'Unknown'}');
          setState(() {
            _statusMessage = "Provisioning completed! Device IP: ${status.deviceIp ?? 'Unknown'}";
          });
          break;
        case WifiConnectionState.connecting:
          addLog('Device is connecting to WiFi...');
          setState(() {
            _statusMessage = "Device connecting to WiFi...";
          });
          // Check again after a delay
          await Future.delayed(const Duration(seconds: 3));
          await _checkWiFiStatus();
          break;
        case WifiConnectionState.disconnected:
          addLog('Device disconnected from WiFi');
          setState(() {
            _statusMessage = "Device disconnected from WiFi";
          });
          break;
        case WifiConnectionState.connectionFailed:
          String reason = 'Unknown error';
          if (status.failedReason == WifiConnectFailedReason.authError) {
            reason = 'Authentication error (wrong password)';
          } else if (status.failedReason == WifiConnectFailedReason.networkNotFound) {
            reason = 'Network not found';
          }
          addLog('WiFi connection failed: $reason');
          setState(() {
            _statusMessage = "WiFi connection failed: $reason";
          });
          break;
      }
    } catch (e) {
      addLog('Error checking WiFi status: $e');
      setState(() {
        _statusMessage = "Error checking WiFi status: $e";
      });
    }
  }

  Future<void> _disconnect() async {
    if (_connectedDevice != null) {
      addLog('Disconnecting from device: ${_connectedDevice!.name.isEmpty ? 'Unknown' : _connectedDevice!.name}');

      // Dispose ESP provisioning
      await _espProv?.dispose();
      _espProv = null;

      // Disconnect device
      await _connectedDevice!.disconnect();
      addLog('Device disconnected successfully');

      setState(() {
        _connectedDevice = null;
        _isConnected = false;
        _sessionEstablished = false;
        _wifiConfigSent = false;
        _wifiConfigApplied = false;
        _wifiNetworks.clear();
        _selectedSSID = '';
        _wifiPassword = '';
        _passwordController.clear();
        _statusMessage = "Disconnected";
      });
    }
  }

  void _selectWiFiNetwork(String ssid) {
    setState(() {
      _selectedSSID = ssid;
      _statusMessage = "Selected WiFi: $ssid";
    });
    addLog('Selected WiFi network: $ssid');
  }

  void _showWiFiPasswordDialog(String ssid) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Connect to $ssid'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: _passwordController,
                decoration: const InputDecoration(
                  labelText: 'WiFi Password',
                  hintText: 'Enter WiFi password',
                ),
                obscureText: true,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _wifiPassword = _passwordController.text;
                });
                Navigator.of(context).pop();
                _sendWiFiConfig();
              },
              child: const Text('Connect'),
            ),
          ],
        );
      },
    );
  }







  void _showLogsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Debug Logs'),
          content: SizedBox(
            width: double.maxFinite,
            height: 400,
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('Total logs: ${_debugLogs.length}'),
                    ElevatedButton(
                      onPressed: () {
                        setState(() {
                          _debugLogs.clear();
                        });
                        Navigator.of(context).pop();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Clear Logs'),
                    ),
                  ],
                ),
                const SizedBox(height: 10),
                Expanded(
                  child: ListView.builder(
                    itemCount: _debugLogs.length,
                    itemBuilder: (context, index) {
                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 2.0),
                        child: Text(
                          _debugLogs[index],
                          style: const TextStyle(
                            fontFamily: 'monospace',
                            fontSize: 12,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Close'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('ESP32 Provisioning'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Status Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Status',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 8),
                    Text(_statusMessage),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // PoP Configuration
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Proof of Possession (PoP)',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _popController,
                      decoration: const InputDecoration(
                        labelText: 'PoP',
                        hintText: 'Enter device PoP (e.g., abcd1234)',
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Control Buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isScanning ? null : _startScan,
                    child: _isScanning
                        ? const Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(strokeWidth: 2),
                              ),
                              SizedBox(width: 8),
                              Text('Scanning...'),
                            ],
                          )
                        : const Text('Scan for Devices'),
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _isConnected ? _disconnect : null,
                  child: const Text('Disconnect'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: () => _showLogsDialog(context),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('View Logs'),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Discovered Devices
            Expanded(
              child: Card(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Text(
                        'Discovered Devices',
                        style: Theme.of(context).textTheme.headlineSmall,
                      ),
                    ),
                    Expanded(
                      child: ListView.builder(
                        itemCount: _discoveredDevices.length,
                        itemBuilder: (context, index) {
                          final device = _discoveredDevices[index];
                          final isConnected = _connectedDevice == device;

                          return ListTile(
                            title: Text(device.name.isEmpty
                                ? 'Unknown Device'
                                : device.name),
                            subtitle: Text(device.id.toString()),
                            trailing: isConnected
                                ? const Icon(Icons.bluetooth_connected,
                                    color: Colors.green)
                                : ElevatedButton(
                                    onPressed: () => _connectToDevice(device),
                                    child: const Text('Connect'),
                                  ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Provisioning Steps
            if (_isConnected) ...[
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: !_sessionEstablished ? _establishSession : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: _sessionEstablished ? Colors.green : Colors.blue,
                        foregroundColor: Colors.white,
                      ),
                      child: Text(_sessionEstablished ? 'Session Established' : 'Establish Session'),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _sessionEstablished && !_wifiConfigSent ? _scanWiFiNetworks : null,
                      child: const Text('Scan WiFi'),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
            ],

            // WiFi Networks
            if (_wifiNetworks.isNotEmpty) ...[
              Card(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Text(
                        'WiFi Networks',
                        style: Theme.of(context).textTheme.headlineSmall,
                      ),
                    ),
                    SizedBox(
                      height: 150,
                      child: ListView.builder(
                        itemCount: _wifiNetworks.length,
                        itemBuilder: (context, index) {
                          final network = _wifiNetworks[index];
                          final isSelected = _selectedSSID == network.ssid;

                          return ListTile(
                            title: Text(network.ssid),
                            subtitle: Text('RSSI: ${network.rssi} dBm'),
                            trailing: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                if (network.private) const Icon(Icons.lock, size: 16),
                                const SizedBox(width: 8),
                                Icon(
                                  Icons.signal_wifi_4_bar,
                                  color: network.rssi > -50 ? Colors.green :
                                         network.rssi > -70 ? Colors.orange : Colors.red,
                                ),
                              ],
                            ),
                            selected: isSelected,
                            onTap: () {
                              _selectWiFiNetwork(network.ssid);
                              if (network.private) {
                                _showWiFiPasswordDialog(network.ssid);
                              } else {
                                setState(() {
                                  _wifiPassword = '';
                                });
                                _sendWiFiConfig();
                              }
                            },
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 8),
            ],

            // WiFi Configuration Status
            if (_wifiConfigSent) ...[
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _wifiConfigSent && !_wifiConfigApplied ? _applyWiFiConfig : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: _wifiConfigApplied ? Colors.green : Colors.orange,
                        foregroundColor: Colors.white,
                      ),
                      child: Text(_wifiConfigApplied ? 'Config Applied' : 'Apply WiFi Config'),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _wifiConfigApplied ? _checkWiFiStatus : null,
                      child: const Text('Check Status'),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }
}
