import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'esp_provisioning/esp_provisioning.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'ESP32 Provisioning',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
      ),
      home: const ESP32ProvisioningPage(),
    );
  }
}

class ESP32ProvisioningPage extends StatefulWidget {
  const ESP32ProvisioningPage({super.key});

  @override
  State<ESP32ProvisioningPage> createState() => _ESP32ProvisioningPageState();
}

class _ESP32ProvisioningPageState extends State<ESP32ProvisioningPage> {
  // BLE Mesh Provisioning Service UUIDs
  static const String MESH_PROV_SERVICE_UUID = "00001827-0000-1000-8000-00805f9b34fb";

  List<BluetoothDevice> _discoveredDevices = [];
  BluetoothDevice? _connectedDevice;
  bool _isScanning = false;
  bool _isConnected = false;
  bool _isProvisioning = false;
  String _statusMessage = "Ready to scan for devices";

  // Debug logging
  final List<String> _debugLogs = [];

  // Mesh Provisioning
  MeshProvisioner? _meshProvisioner;
  MeshProvisioningStatus _provisioningStatus = MeshProvisioningStatus.idle;
  final List<String> _provisioningLogs = [];

  // Mesh network configuration
  final List<int> _networkKey = List.generate(16, (index) => Random().nextInt(256));
  final List<int> _appKey = List.generate(16, (index) => Random().nextInt(256));
  final int _networkKeyIndex = 0;
  final int _appKeyIndex = 0;
  final int _unicastAddress = 0x0001;
  final List<int> _ivIndex = [0x00, 0x00, 0x00, 0x00];

  void addLog(String log) {
    setState(() {
      _debugLogs.add('[${DateTime.now().toIso8601String()}] $log');
      if (_debugLogs.length > 200) {
        _debugLogs.removeAt(0); // Limit log size
      }
    });
  }

  @override
  void initState() {
    super.initState();
    addLog('BLE Mesh Provisioning App initialized');
    _initBLE();
  }

  @override
  void dispose() {
    _meshProvisioner?.dispose();
    super.dispose();
  }

  Future<void> _initBLE() async {
    addLog('Initializing BLE');
    // Request permissions
    await _requestPermissions();

    // Check if Bluetooth is available
    if (await FlutterBluePlus.isAvailable == false) {
      addLog('Bluetooth not available');
      setState(() {
        _statusMessage = "Bluetooth not available";
      });
      return;
    }

    // Turn on Bluetooth if it's off
    addLog('Turning on Bluetooth');
    await FlutterBluePlus.turnOn();
    addLog('BLE initialization completed');
  }

  Future<void> _requestPermissions() async {
    addLog('Requesting permissions');
    await [
      Permission.bluetooth,
      Permission.bluetoothScan,
      Permission.bluetoothConnect,
      Permission.location,
    ].request();
    addLog('Permissions requested');
  }

  Future<void> _startScan() async {
    if (_isScanning) return;

    addLog('Starting BLE scan for mesh devices');
    setState(() {
      _isScanning = true;
      _discoveredDevices.clear();
      _statusMessage = "Scanning for BLE Mesh devices...";
    });

    try {
      // Start scanning for devices with mesh provisioning service
      addLog('Scanning for devices with mesh provisioning service');
      FlutterBluePlus.startScan(
        withServices: [Guid(MESH_PROV_SERVICE_UUID)],
        timeout: const Duration(seconds: 10),
      );

      // Listen for scan results
      FlutterBluePlus.scanResults.listen((results) {
        for (ScanResult result in results) {
          if (!_discoveredDevices.contains(result.device)) {
            addLog('Found device: ${result.device.name.isEmpty ? 'Unknown' : result.device.name} (${result.device.id})');
            setState(() {
              _discoveredDevices.add(result.device);
            });
          }
        }
      });

      // Stop scanning after timeout
      await Future.delayed(const Duration(seconds: 10));
      await FlutterBluePlus.stopScan();
      addLog('Scan completed');
    } catch (e) {
      addLog('Error during scan: $e');
      setState(() {
        _statusMessage = "Error during scan: $e";
      });
    }

    setState(() {
      _isScanning = false;
      if (_discoveredDevices.isEmpty) {
        addLog('No BLE Mesh devices found');
        _statusMessage = "No BLE Mesh devices found";
      } else {
        addLog('Found ${_discoveredDevices.length} mesh device(s)');
        _statusMessage = "Found ${_discoveredDevices.length} mesh device(s)";
      }
    });
  }

  Future<void> _connectToDevice(BluetoothDevice device) async {
    if (_isConnected) return;

    addLog('Attempting to connect to mesh device: ${device.name.isEmpty ? 'Unknown' : device.name} (${device.id})');
    setState(() {
      _statusMessage = "Connecting to ${device.name}...";
    });

    try {
      // Create mesh provisioning instance
      final transport = TransportBLE(device);
      final networkConfig = MeshNetworkConfig(
        networkKey: Uint8List.fromList(_networkKey),
        applicationKey: Uint8List.fromList(_appKey),
        networkKeyIndex: _networkKeyIndex,
        applicationKeyIndex: _appKeyIndex,
        unicastAddress: _unicastAddress,
        ivIndex: Uint8List.fromList(_ivIndex),
      );
      final security = MeshProvisioningSecurity(networkConfig: networkConfig);
      _meshProvisioner = MeshProvisioner(transport: transport, security: security);

      // Listen to provisioning status and logs
      _meshProvisioner!.statusStream.listen((status) {
        setState(() {
          _provisioningStatus = status;
          _statusMessage = "Provisioning status: ${status.name}";
        });
      });

      _meshProvisioner!.logStream.listen((log) {
        addLog(log);
        setState(() {
          _provisioningLogs.add(log);
        });
      });

      addLog('Successfully connected to mesh device');
      setState(() {
        _connectedDevice = device;
        _isConnected = true;
        _statusMessage = "Connected to ${device.name}. Ready to start provisioning.";
      });
    } catch (e) {
      addLog('Failed to connect: $e');
      setState(() {
        _statusMessage = "Failed to connect: $e";
      });
    }
  }

  Future<void> _startMeshProvisioning() async {
    if (_meshProvisioner == null) {
      addLog('Mesh provisioner not initialized');
      return;
    }

    addLog('Starting mesh provisioning...');
    setState(() {
      _isProvisioning = true;
      _statusMessage = "Starting mesh provisioning...";
    });

    try {
      final result = await _meshProvisioner!.startProvisioning();

      switch (result.status) {
        case MeshProvisioningStatus.complete:
          addLog('Mesh provisioning completed successfully!');
          addLog('Network Key: ${_networkKey.map((e) => e.toRadixString(16).padLeft(2, '0')).join(':')}');
          addLog('App Key: ${_appKey.map((e) => e.toRadixString(16).padLeft(2, '0')).join(':')}');
          addLog('Unicast Address: 0x${_unicastAddress.toRadixString(16).padLeft(4, '0')}');
          setState(() {
            _statusMessage = "Mesh provisioning completed successfully!";
          });
          break;
        case MeshProvisioningStatus.failed:
          addLog('Mesh provisioning failed: ${result.errorMessage}');
          setState(() {
            _statusMessage = "Mesh provisioning failed: ${result.errorMessage}";
          });
          break;
        default:
          addLog('Unexpected provisioning status: ${result.status}');
          setState(() {
            _statusMessage = "Unexpected status: ${result.status.name}";
          });
      }
    } catch (e) {
      addLog('Error during mesh provisioning: $e');
      setState(() {
        _statusMessage = "Error during mesh provisioning: $e";
      });
    } finally {
      setState(() {
        _isProvisioning = false;
      });
    }
  }

  Future<void> _disconnect() async {
    if (_connectedDevice != null) {
      addLog('Disconnecting from device: ${_connectedDevice!.name.isEmpty ? 'Unknown' : _connectedDevice!.name}');

      // Dispose mesh provisioner
      _meshProvisioner?.dispose();
      _meshProvisioner = null;

      // Disconnect device
      await _connectedDevice!.disconnect();
      addLog('Device disconnected successfully');

      setState(() {
        _connectedDevice = null;
        _isConnected = false;
        _isProvisioning = false;
        _provisioningStatus = MeshProvisioningStatus.idle;
        _provisioningLogs.clear();
        _statusMessage = "Disconnected";
      });
    }
  }





  void _showLogsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Debug Logs'),
          content: SizedBox(
            width: double.maxFinite,
            height: 400,
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('Total logs: ${_debugLogs.length}'),
                    ElevatedButton(
                      onPressed: () {
                        setState(() {
                          _debugLogs.clear();
                        });
                        Navigator.of(context).pop();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Clear Logs'),
                    ),
                  ],
                ),
                const SizedBox(height: 10),
                Expanded(
                  child: ListView.builder(
                    itemCount: _debugLogs.length,
                    itemBuilder: (context, index) {
                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 2.0),
                        child: Text(
                          _debugLogs[index],
                          style: const TextStyle(
                            fontFamily: 'monospace',
                            fontSize: 12,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Close'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('BLE Mesh Provisioning'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Status Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Status',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 8),
                    Text(_statusMessage),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Mesh Network Configuration
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Mesh Network Configuration',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 8),
                    Text('Network Key: ${_networkKey.map((e) => e.toRadixString(16).padLeft(2, '0')).join(':')}'),
                    Text('App Key: ${_appKey.map((e) => e.toRadixString(16).padLeft(2, '0')).join(':')}'),
                    Text('Unicast Address: 0x${_unicastAddress.toRadixString(16).padLeft(4, '0')}'),
                    Text('Net Key Index: $_networkKeyIndex'),
                    Text('App Key Index: $_appKeyIndex'),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Control Buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isScanning ? null : _startScan,
                    child: _isScanning
                        ? const Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(strokeWidth: 2),
                              ),
                              SizedBox(width: 8),
                              Text('Scanning...'),
                            ],
                          )
                        : const Text('Scan for Mesh Devices'),
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _isConnected ? _disconnect : null,
                  child: const Text('Disconnect'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: () => _showLogsDialog(context),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('View Logs'),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Discovered Devices
            Expanded(
              child: Card(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Text(
                        'Discovered Devices',
                        style: Theme.of(context).textTheme.headlineSmall,
                      ),
                    ),
                    Expanded(
                      child: ListView.builder(
                        itemCount: _discoveredDevices.length,
                        itemBuilder: (context, index) {
                          final device = _discoveredDevices[index];
                          final isConnected = _connectedDevice == device;

                          return ListTile(
                            title: Text(device.name.isEmpty
                                ? 'Unknown Device'
                                : device.name),
                            subtitle: Text(device.id.toString()),
                            trailing: isConnected
                                ? const Icon(Icons.bluetooth_connected,
                                    color: Colors.green)
                                : ElevatedButton(
                                    onPressed: () => _connectToDevice(device),
                                    child: const Text('Connect'),
                                  ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Mesh Provisioning
            if (_isConnected) ...[
              ElevatedButton(
                onPressed: !_isProvisioning && _provisioningStatus != MeshProvisioningStatus.complete
                    ? _startMeshProvisioning : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _provisioningStatus == MeshProvisioningStatus.complete
                      ? Colors.green : Colors.blue,
                  foregroundColor: Colors.white,
                ),
                child: _isProvisioning
                    ? const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          ),
                          SizedBox(width: 8),
                          Text('Provisioning...'),
                        ],
                      )
                    : Text(_provisioningStatus == MeshProvisioningStatus.complete
                        ? 'Provisioning Complete' : 'Start Mesh Provisioning'),
              ),
              const SizedBox(height: 8),
            ],

            // Provisioning Status
            if (_provisioningStatus != MeshProvisioningStatus.idle) ...[
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Provisioning Status',
                        style: Theme.of(context).textTheme.headlineSmall,
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Icon(
                            _getStatusIcon(_provisioningStatus),
                            color: _getStatusColor(_provisioningStatus),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            _getStatusText(_provisioningStatus),
                            style: TextStyle(
                              color: _getStatusColor(_provisioningStatus),
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      if (_provisioningLogs.isNotEmpty) ...[
                        const SizedBox(height: 8),
                        const Text('Recent logs:'),
                        ...(_provisioningLogs.take(3).map((log) => Text(
                          log,
                          style: const TextStyle(fontSize: 12, fontFamily: 'monospace'),
                        ))),
                      ],
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 8),
            ],

          ],
        ),
      ),
    );
  }

  IconData _getStatusIcon(MeshProvisioningStatus status) {
    switch (status) {
      case MeshProvisioningStatus.idle:
        return Icons.radio_button_unchecked;
      case MeshProvisioningStatus.scanning:
        return Icons.search;
      case MeshProvisioningStatus.connecting:
        return Icons.bluetooth_searching;
      case MeshProvisioningStatus.inviting:
        return Icons.mail_outline;
      case MeshProvisioningStatus.exchangingCapabilities:
        return Icons.handshake;
      case MeshProvisioningStatus.exchangingKeys:
        return Icons.key;
      case MeshProvisioningStatus.authenticating:
        return Icons.security;
      case MeshProvisioningStatus.sendingData:
        return Icons.send;
      case MeshProvisioningStatus.complete:
        return Icons.check_circle;
      case MeshProvisioningStatus.failed:
        return Icons.error;
    }
  }

  Color _getStatusColor(MeshProvisioningStatus status) {
    switch (status) {
      case MeshProvisioningStatus.idle:
        return Colors.grey;
      case MeshProvisioningStatus.scanning:
      case MeshProvisioningStatus.connecting:
      case MeshProvisioningStatus.inviting:
      case MeshProvisioningStatus.exchangingCapabilities:
      case MeshProvisioningStatus.exchangingKeys:
      case MeshProvisioningStatus.authenticating:
      case MeshProvisioningStatus.sendingData:
        return Colors.blue;
      case MeshProvisioningStatus.complete:
        return Colors.green;
      case MeshProvisioningStatus.failed:
        return Colors.red;
    }
  }

  String _getStatusText(MeshProvisioningStatus status) {
    switch (status) {
      case MeshProvisioningStatus.idle:
        return 'Idle';
      case MeshProvisioningStatus.scanning:
        return 'Scanning for devices';
      case MeshProvisioningStatus.connecting:
        return 'Connecting to device';
      case MeshProvisioningStatus.inviting:
        return 'Sending invitation';
      case MeshProvisioningStatus.exchangingCapabilities:
        return 'Exchanging capabilities';
      case MeshProvisioningStatus.exchangingKeys:
        return 'Exchanging keys';
      case MeshProvisioningStatus.authenticating:
        return 'Authenticating';
      case MeshProvisioningStatus.sendingData:
        return 'Sending provisioning data';
      case MeshProvisioningStatus.complete:
        return 'Provisioning complete!';
      case MeshProvisioningStatus.failed:
        return 'Provisioning failed';
    }
  }
}
