// BLE Mesh Provisioning Models and PDU structures
import 'dart:typed_data';

// Mesh Provisioning PDU Types
enum MeshProvisioningPDUType {
  invite(0x00),
  capabilities(0x01),
  start(0x02),
  publicKey(0x03),
  inputComplete(0x04),
  confirmation(0x05),
  random(0x06),
  data(0x07),
  complete(0x08),
  failed(0x09);

  const MeshProvisioningPDUType(this.value);
  final int value;
}

// Mesh Provisioning Error Codes
enum MeshProvisioningErrorCode {
  prohibitedError(0x00),
  invalidPDU(0x01),
  invalidFormat(0x02),
  unexpectedPDU(0x03),
  confirmationFailed(0x04),
  outOfResources(0x05),
  decryptionFailed(0x06),
  unexpectedError(0x07),
  cannotAssignAddresses(0x08);

  const MeshProvisioningErrorCode(this.value);
  final int value;
}

// Mesh Provisioning Capabilities
class MeshProvisioningCapabilities {
  final int numberOfElements;
  final int supportedAlgorithms;
  final int publicKeyType;
  final int staticOOBType;
  final int outputOOBSize;
  final int outputOOBAction;
  final int inputOOBSize;
  final int inputOOBAction;

  MeshProvisioningCapabilities({
    required this.numberOfElements,
    required this.supportedAlgorithms,
    required this.publicKeyType,
    required this.staticOOBType,
    required this.outputOOBSize,
    required this.outputOOBAction,
    required this.inputOOBSize,
    required this.inputOOBAction,
  });

  factory MeshProvisioningCapabilities.fromBytes(Uint8List data) {
    if (data.length < 11) {
      throw Exception('Invalid capabilities data length');
    }
    
    return MeshProvisioningCapabilities(
      numberOfElements: data[1],
      supportedAlgorithms: (data[2] << 8) | data[3],
      publicKeyType: data[4],
      staticOOBType: data[5],
      outputOOBSize: data[6],
      outputOOBAction: (data[7] << 8) | data[8],
      inputOOBSize: data[9],
      inputOOBAction: (data[10] << 8) | data[11],
    );
  }

  @override
  String toString() {
    return 'MeshProvisioningCapabilities{elements: $numberOfElements, algorithms: $supportedAlgorithms, publicKeyType: $publicKeyType}';
  }
}

// Mesh Provisioning Data
class MeshProvisioningData {
  final Uint8List networkKey;
  final int keyIndex;
  final int flags;
  final Uint8List ivIndex;
  final int unicastAddress;

  MeshProvisioningData({
    required this.networkKey,
    required this.keyIndex,
    required this.flags,
    required this.ivIndex,
    required this.unicastAddress,
  });

  Uint8List toBytes() {
    final data = Uint8List(25);
    
    // Network Key (16 bytes)
    data.setRange(0, 16, networkKey);
    
    // Key Index (2 bytes)
    data[16] = keyIndex & 0xFF;
    data[17] = (keyIndex >> 8) & 0xFF;
    
    // Flags (1 byte)
    data[18] = flags;
    
    // IV Index (4 bytes)
    data.setRange(19, 23, ivIndex);
    
    // Unicast Address (2 bytes)
    data[23] = unicastAddress & 0xFF;
    data[24] = (unicastAddress >> 8) & 0xFF;
    
    return data;
  }

  @override
  String toString() {
    return 'MeshProvisioningData{keyIndex: $keyIndex, unicastAddress: 0x${unicastAddress.toRadixString(16)}}';
  }
}

// Mesh Provisioning PDU
class MeshProvisioningPDU {
  final MeshProvisioningPDUType type;
  final Uint8List? data;

  MeshProvisioningPDU({
    required this.type,
    this.data,
  });

  factory MeshProvisioningPDU.fromBytes(Uint8List bytes) {
    if (bytes.isEmpty) {
      throw Exception('Empty PDU data');
    }

    final type = MeshProvisioningPDUType.values.firstWhere(
      (t) => t.value == bytes[0],
      orElse: () => throw Exception('Unknown PDU type: ${bytes[0]}'),
    );

    final data = bytes.length > 1 ? bytes.sublist(1) : null;

    return MeshProvisioningPDU(type: type, data: data);
  }

  Uint8List toBytes() {
    if (data == null) {
      return Uint8List.fromList([type.value]);
    }
    
    final result = Uint8List(1 + data!.length);
    result[0] = type.value;
    result.setRange(1, result.length, data!);
    return result;
  }

  @override
  String toString() {
    return 'MeshProvisioningPDU{type: ${type.name}, dataLength: ${data?.length ?? 0}}';
  }
}

// Mesh Network Configuration
class MeshNetworkConfig {
  final Uint8List networkKey;
  final Uint8List applicationKey;
  final int networkKeyIndex;
  final int applicationKeyIndex;
  final int unicastAddress;
  final Uint8List ivIndex;
  final int flags;

  MeshNetworkConfig({
    required this.networkKey,
    required this.applicationKey,
    required this.networkKeyIndex,
    required this.applicationKeyIndex,
    required this.unicastAddress,
    required this.ivIndex,
    this.flags = 0,
  });

  @override
  String toString() {
    return 'MeshNetworkConfig{netKeyIndex: $networkKeyIndex, appKeyIndex: $applicationKeyIndex, unicastAddr: 0x${unicastAddress.toRadixString(16)}}';
  }
}

// Provisioning Status
enum MeshProvisioningStatus {
  idle,
  scanning,
  connecting,
  inviting,
  exchangingCapabilities,
  exchangingKeys,
  authenticating,
  sendingData,
  complete,
  failed,
}

// Connection models for mesh provisioning
class MeshProvisioningResult {
  final MeshProvisioningStatus status;
  final String? errorMessage;
  final MeshNetworkConfig? networkConfig;

  MeshProvisioningResult({
    required this.status,
    this.errorMessage,
    this.networkConfig,
  });

  @override
  String toString() {
    return 'MeshProvisioningResult{status: ${status.name}, error: $errorMessage}';
  }
}
