// Generated protobuf WiFi config messages
import 'dart:core' as $core;
import 'dart:typed_data' as $typed_data;
import 'package:protobuf/protobuf.dart' as $pb;
import 'constants.pbenum.dart' as $0;

class WiFiConfigPayload extends $pb.GeneratedMessage {
  factory WiFiConfigPayload({
    WiFiConfigMsgType? msg,
    CmdSetConfig? cmdSetConfig,
    RespSetConfig? respSetConfig,
    CmdGetStatus? cmdGetStatus,
    RespGetStatus? respGetStatus,
    CmdApplyConfig? cmdApplyConfig,
    RespApplyConfig? respApplyConfig,
  }) {
    final $result = create();
    if (msg != null) {
      $result.msg = msg;
    }
    if (cmdSetConfig != null) {
      $result.cmdSetConfig = cmdSetConfig;
    }
    if (respSetConfig != null) {
      $result.respSetConfig = respSetConfig;
    }
    if (cmdGetStatus != null) {
      $result.cmdGetStatus = cmdGetStatus;
    }
    if (respGetStatus != null) {
      $result.respGetStatus = respGetStatus;
    }
    if (cmdApplyConfig != null) {
      $result.cmdApplyConfig = cmdApplyConfig;
    }
    if (respApplyConfig != null) {
      $result.respApplyConfig = respApplyConfig;
    }
    return $result;
  }
  WiFiConfigPayload._() : super();
  factory WiFiConfigPayload.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory WiFiConfigPayload.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo('WiFiConfigPayload', createEmptyInstance: create)
    ..e<WiFiConfigMsgType>(1, 'msg', $pb.PbFieldType.OE, defaultOrMaker: WiFiConfigMsgType.TypeCmdSetConfig, valueOf: WiFiConfigMsgType.valueOf, enumValues: WiFiConfigMsgType.values)
    ..aOM<CmdSetConfig>(10, 'cmdSetConfig', subBuilder: CmdSetConfig.create)
    ..aOM<RespSetConfig>(11, 'respSetConfig', subBuilder: RespSetConfig.create)
    ..aOM<CmdGetStatus>(12, 'cmdGetStatus', subBuilder: CmdGetStatus.create)
    ..aOM<RespGetStatus>(13, 'respGetStatus', subBuilder: RespGetStatus.create)
    ..aOM<CmdApplyConfig>(14, 'cmdApplyConfig', subBuilder: CmdApplyConfig.create)
    ..aOM<RespApplyConfig>(15, 'respApplyConfig', subBuilder: RespApplyConfig.create)
    ..hasRequiredFields = false;

  @$core.pragma('dart2js:noInline')
  static WiFiConfigPayload create() => WiFiConfigPayload._();
  WiFiConfigPayload createEmptyInstance() => create();
  static $pb.PbList<WiFiConfigPayload> createRepeated() => $pb.PbList<WiFiConfigPayload>();
  @$core.pragma('dart2js:noInline')
  static WiFiConfigPayload getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<WiFiConfigPayload>(create);
  static WiFiConfigPayload? _defaultInstance;

  $pb.BuilderInfo get info_ => _i;

  @$pb.TagNumber(1)
  WiFiConfigMsgType get msg => $_getN(0);
  @$pb.TagNumber(1)
  set msg(WiFiConfigMsgType v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasMsg() => $_has(0);
  @$pb.TagNumber(1)
  void clearMsg() => clearField(1);

  @$pb.TagNumber(10)
  CmdSetConfig get cmdSetConfig => $_getN(1);
  @$pb.TagNumber(10)
  set cmdSetConfig(CmdSetConfig v) { setField(10, v); }
  @$pb.TagNumber(10)
  $core.bool hasCmdSetConfig() => $_has(1);
  @$pb.TagNumber(10)
  void clearCmdSetConfig() => clearField(10);
  @$pb.TagNumber(10)
  CmdSetConfig ensureCmdSetConfig() => $_ensure(1);

  @$pb.TagNumber(11)
  RespSetConfig get respSetConfig => $_getN(2);
  @$pb.TagNumber(11)
  set respSetConfig(RespSetConfig v) { setField(11, v); }
  @$pb.TagNumber(11)
  $core.bool hasRespSetConfig() => $_has(2);
  @$pb.TagNumber(11)
  void clearRespSetConfig() => clearField(11);
  @$pb.TagNumber(11)
  RespSetConfig ensureRespSetConfig() => $_ensure(2);

  @$pb.TagNumber(12)
  CmdGetStatus get cmdGetStatus => $_getN(3);
  @$pb.TagNumber(12)
  set cmdGetStatus(CmdGetStatus v) { setField(12, v); }
  @$pb.TagNumber(12)
  $core.bool hasCmdGetStatus() => $_has(3);
  @$pb.TagNumber(12)
  void clearCmdGetStatus() => clearField(12);
  @$pb.TagNumber(12)
  CmdGetStatus ensureCmdGetStatus() => $_ensure(3);

  @$pb.TagNumber(13)
  RespGetStatus get respGetStatus => $_getN(4);
  @$pb.TagNumber(13)
  set respGetStatus(RespGetStatus v) { setField(13, v); }
  @$pb.TagNumber(13)
  $core.bool hasRespGetStatus() => $_has(4);
  @$pb.TagNumber(13)
  void clearRespGetStatus() => clearField(13);
  @$pb.TagNumber(13)
  RespGetStatus ensureRespGetStatus() => $_ensure(4);

  @$pb.TagNumber(14)
  CmdApplyConfig get cmdApplyConfig => $_getN(5);
  @$pb.TagNumber(14)
  set cmdApplyConfig(CmdApplyConfig v) { setField(14, v); }
  @$pb.TagNumber(14)
  $core.bool hasCmdApplyConfig() => $_has(5);
  @$pb.TagNumber(14)
  void clearCmdApplyConfig() => clearField(14);
  @$pb.TagNumber(14)
  CmdApplyConfig ensureCmdApplyConfig() => $_ensure(5);

  @$pb.TagNumber(15)
  RespApplyConfig get respApplyConfig => $_getN(6);
  @$pb.TagNumber(15)
  set respApplyConfig(RespApplyConfig v) { setField(15, v); }
  @$pb.TagNumber(15)
  $core.bool hasRespApplyConfig() => $_has(6);
  @$pb.TagNumber(15)
  void clearRespApplyConfig() => clearField(15);
  @$pb.TagNumber(15)
  RespApplyConfig ensureRespApplyConfig() => $_ensure(6);
}

class CmdSetConfig extends $pb.GeneratedMessage {
  factory CmdSetConfig({
    $core.List<$core.int>? ssid,
    $core.List<$core.int>? passphrase,
  }) {
    final $result = create();
    if (ssid != null) {
      $result.ssid = ssid;
    }
    if (passphrase != null) {
      $result.passphrase = passphrase;
    }
    return $result;
  }
  CmdSetConfig._() : super();
  factory CmdSetConfig.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory CmdSetConfig.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo('CmdSetConfig', createEmptyInstance: create)
    ..a<$core.List<$core.int>>(1, 'ssid', $pb.PbFieldType.OY)
    ..a<$core.List<$core.int>>(2, 'passphrase', $pb.PbFieldType.OY)
    ..hasRequiredFields = false;

  @$core.pragma('dart2js:noInline')
  static CmdSetConfig create() => CmdSetConfig._();
  CmdSetConfig createEmptyInstance() => create();
  static $pb.PbList<CmdSetConfig> createRepeated() => $pb.PbList<CmdSetConfig>();
  @$core.pragma('dart2js:noInline')
  static CmdSetConfig getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<CmdSetConfig>(create);
  static CmdSetConfig? _defaultInstance;

  $pb.BuilderInfo get info_ => _i;

  @$pb.TagNumber(1)
  $core.List<$core.int> get ssid => $_getN(0);
  @$pb.TagNumber(1)
  set ssid($core.List<$core.int> v) { $_setBytes(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasSsid() => $_has(0);
  @$pb.TagNumber(1)
  void clearSsid() => clearField(1);

  @$pb.TagNumber(2)
  $core.List<$core.int> get passphrase => $_getN(1);
  @$pb.TagNumber(2)
  set passphrase($core.List<$core.int> v) { $_setBytes(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasPassphrase() => $_has(1);
  @$pb.TagNumber(2)
  void clearPassphrase() => clearField(2);
}

class RespSetConfig extends $pb.GeneratedMessage {
  factory RespSetConfig({
    Status? status,
  }) {
    final $result = create();
    if (status != null) {
      $result.status = status;
    }
    return $result;
  }
  RespSetConfig._() : super();
  factory RespSetConfig.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory RespSetConfig.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo('RespSetConfig', createEmptyInstance: create)
    ..e<Status>(1, 'status', $pb.PbFieldType.OE, defaultOrMaker: Status.Success, valueOf: Status.valueOf, enumValues: Status.values)
    ..hasRequiredFields = false;

  @$core.pragma('dart2js:noInline')
  static RespSetConfig create() => RespSetConfig._();
  RespSetConfig createEmptyInstance() => create();
  static $pb.PbList<RespSetConfig> createRepeated() => $pb.PbList<RespSetConfig>();
  @$core.pragma('dart2js:noInline')
  static RespSetConfig getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<RespSetConfig>(create);
  static RespSetConfig? _defaultInstance;

  $pb.BuilderInfo get info_ => _i;

  @$pb.TagNumber(1)
  Status get status => $_getN(0);
  @$pb.TagNumber(1)
  set status(Status v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasStatus() => $_has(0);
  @$pb.TagNumber(1)
  void clearStatus() => clearField(1);
}

// Additional WiFi config classes
class CmdGetStatus extends $pb.GeneratedMessage {
  factory CmdGetStatus() => create();
  CmdGetStatus._() : super();
  factory CmdGetStatus.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory CmdGetStatus.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo('CmdGetStatus', createEmptyInstance: create)
    ..hasRequiredFields = false;

  @$core.pragma('dart2js:noInline')
  static CmdGetStatus create() => CmdGetStatus._();
  CmdGetStatus createEmptyInstance() => create();
  static $pb.PbList<CmdGetStatus> createRepeated() => $pb.PbList<CmdGetStatus>();
  @$core.pragma('dart2js:noInline')
  static CmdGetStatus getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<CmdGetStatus>(create);
  static CmdGetStatus? _defaultInstance;

  $pb.BuilderInfo get info_ => _i;
}

class RespGetStatus extends $pb.GeneratedMessage {
  factory RespGetStatus({
    WifiStationState? staState,
    WifiConnectFailedReason? failReason,
    WifiConnectedState? connected,
  }) {
    final $result = create();
    if (staState != null) {
      $result.staState = staState;
    }
    if (failReason != null) {
      $result.failReason = failReason;
    }
    if (connected != null) {
      $result.connected = connected;
    }
    return $result;
  }
  RespGetStatus._() : super();
  factory RespGetStatus.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory RespGetStatus.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo('RespGetStatus', createEmptyInstance: create)
    ..e<WifiStationState>(1, 'staState', $pb.PbFieldType.OE, defaultOrMaker: WifiStationState.Connected, valueOf: WifiStationState.valueOf, enumValues: WifiStationState.values)
    ..e<WifiConnectFailedReason>(2, 'failReason', $pb.PbFieldType.OE, defaultOrMaker: WifiConnectFailedReason.AuthError, valueOf: WifiConnectFailedReason.valueOf, enumValues: WifiConnectFailedReason.values)
    ..aOM<WifiConnectedState>(3, 'connected', subBuilder: WifiConnectedState.create)
    ..hasRequiredFields = false;

  @$core.pragma('dart2js:noInline')
  static RespGetStatus create() => RespGetStatus._();
  RespGetStatus createEmptyInstance() => create();
  static $pb.PbList<RespGetStatus> createRepeated() => $pb.PbList<RespGetStatus>();
  @$core.pragma('dart2js:noInline')
  static RespGetStatus getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<RespGetStatus>(create);
  static RespGetStatus? _defaultInstance;

  $pb.BuilderInfo get info_ => _i;

  @$pb.TagNumber(1)
  WifiStationState get staState => $_getN(0);
  @$pb.TagNumber(1)
  set staState(WifiStationState v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasStaState() => $_has(0);
  @$pb.TagNumber(1)
  void clearStaState() => clearField(1);

  @$pb.TagNumber(2)
  WifiConnectFailedReason get failReason => $_getN(1);
  @$pb.TagNumber(2)
  set failReason(WifiConnectFailedReason v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasFailReason() => $_has(1);
  @$pb.TagNumber(2)
  void clearFailReason() => clearField(2);

  @$pb.TagNumber(3)
  WifiConnectedState get connected => $_getN(2);
  @$pb.TagNumber(3)
  set connected(WifiConnectedState v) { setField(3, v); }
  @$pb.TagNumber(3)
  $core.bool hasConnected() => $_has(2);
  @$pb.TagNumber(3)
  void clearConnected() => clearField(3);
  @$pb.TagNumber(3)
  WifiConnectedState ensureConnected() => $_ensure(2);
}

class WifiConnectedState extends $pb.GeneratedMessage {
  factory WifiConnectedState({
    $core.String? ip4Addr,
  }) {
    final $result = create();
    if (ip4Addr != null) {
      $result.ip4Addr = ip4Addr;
    }
    return $result;
  }
  WifiConnectedState._() : super();
  factory WifiConnectedState.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory WifiConnectedState.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo('WifiConnectedState', createEmptyInstance: create)
    ..aOS(1, 'ip4Addr')
    ..hasRequiredFields = false;

  @$core.pragma('dart2js:noInline')
  static WifiConnectedState create() => WifiConnectedState._();
  WifiConnectedState createEmptyInstance() => create();
  static $pb.PbList<WifiConnectedState> createRepeated() => $pb.PbList<WifiConnectedState>();
  @$core.pragma('dart2js:noInline')
  static WifiConnectedState getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<WifiConnectedState>(create);
  static WifiConnectedState? _defaultInstance;

  $pb.BuilderInfo get info_ => _i;

  @$pb.TagNumber(1)
  $core.String get ip4Addr => $_getSZ(0);
  @$pb.TagNumber(1)
  set ip4Addr($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasIp4Addr() => $_has(0);
  @$pb.TagNumber(1)
  void clearIp4Addr() => clearField(1);
}

class CmdApplyConfig extends $pb.GeneratedMessage {
  factory CmdApplyConfig() => create();
  CmdApplyConfig._() : super();
  factory CmdApplyConfig.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory CmdApplyConfig.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo('CmdApplyConfig', createEmptyInstance: create)
    ..hasRequiredFields = false;

  @$core.pragma('dart2js:noInline')
  static CmdApplyConfig create() => CmdApplyConfig._();
  CmdApplyConfig createEmptyInstance() => create();
  static $pb.PbList<CmdApplyConfig> createRepeated() => $pb.PbList<CmdApplyConfig>();
  @$core.pragma('dart2js:noInline')
  static CmdApplyConfig getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<CmdApplyConfig>(create);
  static CmdApplyConfig? _defaultInstance;

  $pb.BuilderInfo get info_ => _i;
}

class RespApplyConfig extends $pb.GeneratedMessage {
  factory RespApplyConfig({
    Status? status,
  }) {
    final $result = create();
    if (status != null) {
      $result.status = status;
    }
    return $result;
  }
  RespApplyConfig._() : super();
  factory RespApplyConfig.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory RespApplyConfig.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo('RespApplyConfig', createEmptyInstance: create)
    ..e<Status>(1, 'status', $pb.PbFieldType.OE, defaultOrMaker: Status.Success, valueOf: Status.valueOf, enumValues: Status.values)
    ..hasRequiredFields = false;

  @$core.pragma('dart2js:noInline')
  static RespApplyConfig create() => RespApplyConfig._();
  RespApplyConfig createEmptyInstance() => create();
  static $pb.PbList<RespApplyConfig> createRepeated() => $pb.PbList<RespApplyConfig>();
  @$core.pragma('dart2js:noInline')
  static RespApplyConfig getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<RespApplyConfig>(create);
  static RespApplyConfig? _defaultInstance;

  $pb.BuilderInfo get info_ => _i;

  @$pb.TagNumber(1)
  Status get status => $_getN(0);
  @$pb.TagNumber(1)
  set status(Status v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasStatus() => $_has(0);
  @$pb.TagNumber(1)
  void clearStatus() => clearField(1);
}

// Enums
class WiFiConfigMsgType extends $pb.ProtobufEnum {
  static const WiFiConfigMsgType TypeCmdSetConfig = WiFiConfigMsgType._(0, 'TypeCmdSetConfig');
  static const WiFiConfigMsgType TypeRespSetConfig = WiFiConfigMsgType._(1, 'TypeRespSetConfig');
  static const WiFiConfigMsgType TypeCmdGetStatus = WiFiConfigMsgType._(2, 'TypeCmdGetStatus');
  static const WiFiConfigMsgType TypeRespGetStatus = WiFiConfigMsgType._(3, 'TypeRespGetStatus');
  static const WiFiConfigMsgType TypeCmdApplyConfig = WiFiConfigMsgType._(4, 'TypeCmdApplyConfig');
  static const WiFiConfigMsgType TypeRespApplyConfig = WiFiConfigMsgType._(5, 'TypeRespApplyConfig');

  static const $core.List<WiFiConfigMsgType> values = <WiFiConfigMsgType>[
    TypeCmdSetConfig,
    TypeRespSetConfig,
    TypeCmdGetStatus,
    TypeRespGetStatus,
    TypeCmdApplyConfig,
    TypeRespApplyConfig,
  ];

  static final $core.Map<$core.int, WiFiConfigMsgType> _byValue = $pb.ProtobufEnum.initByValue(values);
  static WiFiConfigMsgType? valueOf($core.int value) => _byValue[value];

  const WiFiConfigMsgType._($core.int v, $core.String n) : super(v, n);
}

class WifiStationState extends $pb.ProtobufEnum {
  static const WifiStationState Connected = WifiStationState._(0, 'Connected');
  static const WifiStationState Connecting = WifiStationState._(1, 'Connecting');
  static const WifiStationState Disconnected = WifiStationState._(2, 'Disconnected');
  static const WifiStationState ConnectionFailed = WifiStationState._(3, 'ConnectionFailed');

  static const $core.List<WifiStationState> values = <WifiStationState>[
    Connected,
    Connecting,
    Disconnected,
    ConnectionFailed,
  ];

  static final $core.Map<$core.int, WifiStationState> _byValue = $pb.ProtobufEnum.initByValue(values);
  static WifiStationState? valueOf($core.int value) => _byValue[value];

  const WifiStationState._($core.int v, $core.String n) : super(v, n);
}

class WifiConnectFailedReason extends $pb.ProtobufEnum {
  static const WifiConnectFailedReason AuthError = WifiConnectFailedReason._(0, 'AuthError');
  static const WifiConnectFailedReason NetworkNotFound = WifiConnectFailedReason._(1, 'NetworkNotFound');

  static const $core.List<WifiConnectFailedReason> values = <WifiConnectFailedReason>[
    AuthError,
    NetworkNotFound,
  ];

  static final $core.Map<$core.int, WifiConnectFailedReason> _byValue = $pb.ProtobufEnum.initByValue(values);
  static WifiConnectFailedReason? valueOf($core.int value) => _byValue[value];

  const WifiConnectFailedReason._($core.int v, $core.String n) : super(v, n);
}

typedef Status = $0.Status;
