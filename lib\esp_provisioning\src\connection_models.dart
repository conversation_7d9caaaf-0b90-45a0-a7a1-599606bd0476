// Connection models for ESP provisioning

enum WifiConnectionState {
  connected,
  connecting,
  disconnected,
  connectionFailed,
}

enum WifiConnectFailedReason {
  authError,
  networkNotFound,
}

enum EstablishSessionStatus {
  connected,
  disconnected,
  keymismatch,
}

class WifiAP {
  final String ssid;
  final int rssi;
  final bool private;
  final bool active;

  WifiAP({
    required this.ssid,
    required this.rssi,
    required this.private,
    this.active = false,
  });

  @override
  String toString() {
    return 'WifiAP{ssid: $ssid, rssi: $rssi, private: $private, active: $active}';
  }
}

class ConnectionStatus {
  final WifiConnectionState state;
  final String? deviceIp;
  final WifiConnectFailedReason? failedReason;

  ConnectionStatus({
    required this.state,
    this.deviceIp,
    this.failedReason,
  });

  @override
  String toString() {
    return 'ConnectionStatus{state: $state, deviceIp: $deviceIp, failedReason: $failedReason}';
  }
}
