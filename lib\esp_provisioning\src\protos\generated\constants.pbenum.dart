// Generated protobuf constants
// Based on ESP-IDF provisioning protocol

import 'dart:core' as $core;
import 'package:protobuf/protobuf.dart' as $pb;

class Status extends $pb.ProtobufEnum {
  static const Status Success = Status._(0, _omitEnumNames ? '' : 'Success');
  static const Status InvalidSecScheme = Status._(1, _omitEnumNames ? '' : 'InvalidSecScheme');
  static const Status InvalidProto = Status._(2, _omitEnumNames ? '' : 'InvalidProto');
  static const Status TooManySessions = Status._(3, _omitEnumNames ? '' : 'TooManySessions');
  static const Status InvalidSession = Status._(4, _omitEnumNames ? '' : 'InvalidSession');
  static const Status InvalidArgument = Status._(5, _omitEnumNames ? '' : 'InvalidArgument');
  static const Status InternalError = Status._(6, _omitEnumNames ? '' : 'InternalError');
  static const Status CryptoError = Status._(7, _omitEnumNames ? '' : 'CryptoError');
  static const Status InvalidState = Status._(8, _omitEnumNames ? '' : 'InvalidState');

  static const $core.List<Status> values = <Status>[
    Success,
    InvalidSecScheme,
    InvalidProto,
    TooManySessions,
    InvalidSession,
    InvalidArgument,
    InternalError,
    CryptoError,
    InvalidState,
  ];

  static final $core.Map<$core.int, Status> _byValue = $pb.ProtobufEnum.initByValue(values);
  static Status? valueOf($core.int value) => _byValue[value];

  const Status._($core.int v, $core.String n) : super(v, n);
}

class SecSchemeVersion extends $pb.ProtobufEnum {
  static const SecSchemeVersion SecScheme0 = SecSchemeVersion._(0, _omitEnumNames ? '' : 'SecScheme0');
  static const SecSchemeVersion SecScheme1 = SecSchemeVersion._(1, _omitEnumNames ? '' : 'SecScheme1');

  static const $core.List<SecSchemeVersion> values = <SecSchemeVersion>[
    SecScheme0,
    SecScheme1,
  ];

  static final $core.Map<$core.int, SecSchemeVersion> _byValue = $pb.ProtobufEnum.initByValue(values);
  static SecSchemeVersion? valueOf($core.int value) => _byValue[value];

  const SecSchemeVersion._($core.int v, $core.String n) : super(v, n);
}

const _omitEnumNames = $core.bool.fromEnvironment('protobuf.omit_enum_names');
