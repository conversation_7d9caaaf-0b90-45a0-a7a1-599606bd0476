// Security1 implementation for ESP provisioning
import 'dart:convert';
import 'dart:math';
import 'dart:typed_data';
import 'package:cryptography/cryptography.dart';
import 'package:crypto/crypto.dart';
import 'security.dart';
import 'protos/generated/session.pb.dart';
import 'protos/generated/constants.pbenum.dart';

class Security1 extends ProvSecurity {
  final String pop;
  late Uint8List _clientPrivateKey;
  late Uint8List _clientPublicKey;
  Uint8List? _devicePublicKey;
  Uint8List? _sharedSecret;
  Uint8List? _sessionKey;
  bool _sessionEstablished = false;

  Security1({required this.pop}) {
    _generateKeyPair();
  }

  void _generateKeyPair() {
    // Generate a simple key pair for demonstration
    // In production, use proper Curve25519 key generation
    final random = Random.secure();
    _clientPrivateKey = Uint8List.fromList(
        List.generate(32, (index) => random.nextInt(256)));
    _clientPublicKey = Uint8List.fromList(
        List.generate(32, (index) => random.nextInt(256)));
  }

  @override
  Future<SessionData?> securitySession(SessionData? responseData) async {
    if (_sessionEstablished) {
      return null; // Session already established
    }

    if (responseData == null) {
      // First request - send client public key
      return _createInitialRequest();
    }

    if (responseData.hasSecVer() && responseData.secVer == SecSchemeVersion.SecScheme1) {
      if (responseData.hasSec1()) {
        return await _handleSec1Response(responseData.sec1);
      }
    }

    throw Exception('Invalid session response');
  }

  SessionData _createInitialRequest() {
    final sessionData = SessionData();
    sessionData.secVer = SecSchemeVersion.SecScheme1;
    
    final sec1Payload = Sec1Payload();
    sec1Payload.msg = Sec1MsgType.Session_Command;
    
    final sessionCmd = SessionCmd();
    sessionCmd.clientVerifyData = _clientPublicKey;
    sec1Payload.sc = sessionCmd;
    
    sessionData.sec1 = sec1Payload;
    return sessionData;
  }

  Future<SessionData?> _handleSec1Response(Sec1Payload sec1Response) async {
    if (sec1Response.msg == Sec1MsgType.Session_Response && sec1Response.hasSr()) {
      final sessionResp = sec1Response.sr;
      
      if (sessionResp.status == Status.Success) {
        if (sessionResp.hasDeviceVerifyData()) {
          _devicePublicKey = Uint8List.fromList(sessionResp.deviceVerifyData);
          await _deriveSessionKey();
          _sessionEstablished = true;
          return null; // Session established
        }
      }
    }
    
    throw Exception('Failed to establish session');
  }

  Future<void> _deriveSessionKey() async {
    if (_devicePublicKey == null) {
      throw Exception('Device public key not available');
    }

    // Simplified key derivation - in production use proper ECDH
    final combined = Uint8List.fromList([
      ..._clientPrivateKey,
      ..._devicePublicKey!,
      ...utf8.encode(pop),
    ]);
    
    final digest = sha256.convert(combined);
    _sharedSecret = Uint8List.fromList(digest.bytes);
    
    // Derive session key from shared secret
    final sessionKeyDigest = sha256.convert([..._sharedSecret!, ...utf8.encode('session')]);
    _sessionKey = Uint8List.fromList(sessionKeyDigest.bytes.take(16).toList());
  }

  @override
  Future<Uint8List> encrypt(Uint8List data) async {
    if (!_sessionEstablished || _sessionKey == null) {
      throw Exception('Session not established');
    }

    // Simplified encryption - in production use proper AES-CTR
    final encrypted = Uint8List(data.length);
    for (int i = 0; i < data.length; i++) {
      encrypted[i] = data[i] ^ _sessionKey![i % _sessionKey!.length];
    }
    
    return encrypted;
  }

  @override
  Future<Uint8List> decrypt(Uint8List data) async {
    if (!_sessionEstablished || _sessionKey == null) {
      throw Exception('Session not established');
    }

    // Simplified decryption - same as encryption for XOR
    return await encrypt(data);
  }
}
