// Generated protobuf WiFi scan messages
import 'dart:core' as $core;
import 'dart:typed_data' as $typed_data;
import 'package:protobuf/protobuf.dart' as $pb;
import 'constants.pbenum.dart' as $0;

class WiFiScanPayload extends $pb.GeneratedMessage {
  factory WiFiScanPayload({
    WiFiScanMsgType? msg,
    CmdScanStart? cmdScanStart,
    RespScanStart? respScanStart,
    CmdScanStatus? cmdScanStatus,
    RespScanStatus? respScanStatus,
    CmdScanResult? cmdScanResult,
    RespScanResult? respScanResult,
  }) {
    final $result = create();
    if (msg != null) {
      $result.msg = msg;
    }
    if (cmdScanStart != null) {
      $result.cmdScanStart = cmdScanStart;
    }
    if (respScanStart != null) {
      $result.respScanStart = respScanStart;
    }
    if (cmdScanStatus != null) {
      $result.cmdScanStatus = cmdScanStatus;
    }
    if (respScanStatus != null) {
      $result.respScanStatus = respScanStatus;
    }
    if (cmdScanResult != null) {
      $result.cmdScanResult = cmdScanResult;
    }
    if (respScanResult != null) {
      $result.respScanResult = respScanResult;
    }
    return $result;
  }
  WiFiScanPayload._() : super();
  factory WiFiScanPayload.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory WiFiScanPayload.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo('WiFiScanPayload', createEmptyInstance: create)
    ..e<WiFiScanMsgType>(1, 'msg', $pb.PbFieldType.OE, defaultOrMaker: WiFiScanMsgType.TypeCmdScanStart, valueOf: WiFiScanMsgType.valueOf, enumValues: WiFiScanMsgType.values)
    ..aOM<CmdScanStart>(10, 'cmdScanStart', subBuilder: CmdScanStart.create)
    ..aOM<RespScanStart>(11, 'respScanStart', subBuilder: RespScanStart.create)
    ..aOM<CmdScanStatus>(12, 'cmdScanStatus', subBuilder: CmdScanStatus.create)
    ..aOM<RespScanStatus>(13, 'respScanStatus', subBuilder: RespScanStatus.create)
    ..aOM<CmdScanResult>(14, 'cmdScanResult', subBuilder: CmdScanResult.create)
    ..aOM<RespScanResult>(15, 'respScanResult', subBuilder: RespScanResult.create)
    ..hasRequiredFields = false;

  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
      'Will be removed in next major version')
  WiFiScanPayload clone() => WiFiScanPayload()..mergeFromMessage(this);
  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
      'Will be removed in next major version')
  WiFiScanPayload copyWith(void Function(WiFiScanPayload) updates) => super.copyWith((message) => updates(message as WiFiScanPayload)) as WiFiScanPayload;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static WiFiScanPayload create() => WiFiScanPayload._();
  WiFiScanPayload createEmptyInstance() => create();
  static $pb.PbList<WiFiScanPayload> createRepeated() => $pb.PbList<WiFiScanPayload>();
  @$core.pragma('dart2js:noInline')
  static WiFiScanPayload getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<WiFiScanPayload>(create);
  static WiFiScanPayload? _defaultInstance;

  @$pb.TagNumber(1)
  WiFiScanMsgType get msg => $_getN(0);
  @$pb.TagNumber(1)
  set msg(WiFiScanMsgType v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasMsg() => $_has(0);
  @$pb.TagNumber(1)
  void clearMsg() => clearField(1);

  @$pb.TagNumber(10)
  CmdScanStart get cmdScanStart => $_getN(1);
  @$pb.TagNumber(10)
  set cmdScanStart(CmdScanStart v) { setField(10, v); }
  @$pb.TagNumber(10)
  $core.bool hasCmdScanStart() => $_has(1);
  @$pb.TagNumber(10)
  void clearCmdScanStart() => clearField(10);
  @$pb.TagNumber(10)
  CmdScanStart ensureCmdScanStart() => $_ensure(1);

  @$pb.TagNumber(11)
  RespScanStart get respScanStart => $_getN(2);
  @$pb.TagNumber(11)
  set respScanStart(RespScanStart v) { setField(11, v); }
  @$pb.TagNumber(11)
  $core.bool hasRespScanStart() => $_has(2);
  @$pb.TagNumber(11)
  void clearRespScanStart() => clearField(11);
  @$pb.TagNumber(11)
  RespScanStart ensureRespScanStart() => $_ensure(2);

  @$pb.TagNumber(12)
  CmdScanStatus get cmdScanStatus => $_getN(3);
  @$pb.TagNumber(12)
  set cmdScanStatus(CmdScanStatus v) { setField(12, v); }
  @$pb.TagNumber(12)
  $core.bool hasCmdScanStatus() => $_has(3);
  @$pb.TagNumber(12)
  void clearCmdScanStatus() => clearField(12);
  @$pb.TagNumber(12)
  CmdScanStatus ensureCmdScanStatus() => $_ensure(3);

  @$pb.TagNumber(13)
  RespScanStatus get respScanStatus => $_getN(4);
  @$pb.TagNumber(13)
  set respScanStatus(RespScanStatus v) { setField(13, v); }
  @$pb.TagNumber(13)
  $core.bool hasRespScanStatus() => $_has(4);
  @$pb.TagNumber(13)
  void clearRespScanStatus() => clearField(13);
  @$pb.TagNumber(13)
  RespScanStatus ensureRespScanStatus() => $_ensure(4);

  @$pb.TagNumber(14)
  CmdScanResult get cmdScanResult => $_getN(5);
  @$pb.TagNumber(14)
  set cmdScanResult(CmdScanResult v) { setField(14, v); }
  @$pb.TagNumber(14)
  $core.bool hasCmdScanResult() => $_has(5);
  @$pb.TagNumber(14)
  void clearCmdScanResult() => clearField(14);
  @$pb.TagNumber(14)
  CmdScanResult ensureCmdScanResult() => $_ensure(5);

  @$pb.TagNumber(15)
  RespScanResult get respScanResult => $_getN(6);
  @$pb.TagNumber(15)
  set respScanResult(RespScanResult v) { setField(15, v); }
  @$pb.TagNumber(15)
  $core.bool hasRespScanResult() => $_has(6);
  @$pb.TagNumber(15)
  void clearRespScanResult() => clearField(15);
  @$pb.TagNumber(15)
  RespScanResult ensureRespScanResult() => $_ensure(6);
}

class CmdScanStart extends $pb.GeneratedMessage {
  factory CmdScanStart({
    $core.bool? blocking,
    $core.bool? passive,
    $core.int? groupChannels,
    $core.int? periodMs,
  }) {
    final $result = create();
    if (blocking != null) {
      $result.blocking = blocking;
    }
    if (passive != null) {
      $result.passive = passive;
    }
    if (groupChannels != null) {
      $result.groupChannels = groupChannels;
    }
    if (periodMs != null) {
      $result.periodMs = periodMs;
    }
    return $result;
  }
  CmdScanStart._() : super();
  factory CmdScanStart.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory CmdScanStart.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo('CmdScanStart', createEmptyInstance: create)
    ..aOB(1, 'blocking')
    ..aOB(2, 'passive')
    ..a<$core.int>(3, 'groupChannels', $pb.PbFieldType.OU3)
    ..a<$core.int>(4, 'periodMs', $pb.PbFieldType.OU3)
    ..hasRequiredFields = false;

  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
      'Will be removed in next major version')
  CmdScanStart clone() => CmdScanStart()..mergeFromMessage(this);
  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
      'Will be removed in next major version')
  CmdScanStart copyWith(void Function(CmdScanStart) updates) => super.copyWith((message) => updates(message as CmdScanStart)) as CmdScanStart;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static CmdScanStart create() => CmdScanStart._();
  CmdScanStart createEmptyInstance() => create();
  static $pb.PbList<CmdScanStart> createRepeated() => $pb.PbList<CmdScanStart>();
  @$core.pragma('dart2js:noInline')
  static CmdScanStart getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<CmdScanStart>(create);
  static CmdScanStart? _defaultInstance;

  @$pb.TagNumber(1)
  $core.bool get blocking => $_getBF(0);
  @$pb.TagNumber(1)
  set blocking($core.bool v) { $_setBool(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasBlocking() => $_has(0);
  @$pb.TagNumber(1)
  void clearBlocking() => clearField(1);

  @$pb.TagNumber(2)
  $core.bool get passive => $_getBF(1);
  @$pb.TagNumber(2)
  set passive($core.bool v) { $_setBool(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasPassive() => $_has(1);
  @$pb.TagNumber(2)
  void clearPassive() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get groupChannels => $_getIZ(2);
  @$pb.TagNumber(3)
  set groupChannels($core.int v) { $_setUnsignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasGroupChannels() => $_has(2);
  @$pb.TagNumber(3)
  void clearGroupChannels() => clearField(3);

  @$pb.TagNumber(4)
  $core.int get periodMs => $_getIZ(3);
  @$pb.TagNumber(4)
  set periodMs($core.int v) { $_setUnsignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasPeriodMs() => $_has(3);
  @$pb.TagNumber(4)
  void clearPeriodMs() => clearField(4);
}

// Additional protobuf classes for WiFi scanning
class RespScanStart extends $pb.GeneratedMessage {
  factory RespScanStart() => create();
  RespScanStart._() : super();
  factory RespScanStart.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory RespScanStart.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo('RespScanStart', createEmptyInstance: create)
    ..hasRequiredFields = false;

  @$core.pragma('dart2js:noInline')
  static RespScanStart create() => RespScanStart._();
  RespScanStart createEmptyInstance() => create();
  static $pb.PbList<RespScanStart> createRepeated() => $pb.PbList<RespScanStart>();
  @$core.pragma('dart2js:noInline')
  static RespScanStart getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<RespScanStart>(create);
  static RespScanStart? _defaultInstance;

  $pb.BuilderInfo get info_ => _i;
}

class CmdScanStatus extends $pb.GeneratedMessage {
  factory CmdScanStatus() => create();
  CmdScanStatus._() : super();
  factory CmdScanStatus.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory CmdScanStatus.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo('CmdScanStatus', createEmptyInstance: create)
    ..hasRequiredFields = false;

  @$core.pragma('dart2js:noInline')
  static CmdScanStatus create() => CmdScanStatus._();
  CmdScanStatus createEmptyInstance() => create();
  static $pb.PbList<CmdScanStatus> createRepeated() => $pb.PbList<CmdScanStatus>();
  @$core.pragma('dart2js:noInline')
  static CmdScanStatus getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<CmdScanStatus>(create);
  static CmdScanStatus? _defaultInstance;

  $pb.BuilderInfo get info_ => _i;
}

class RespScanStatus extends $pb.GeneratedMessage {
  factory RespScanStatus({
    $core.bool? scanFinished,
    $core.int? resultCount,
  }) {
    final $result = create();
    if (scanFinished != null) {
      $result.scanFinished = scanFinished;
    }
    if (resultCount != null) {
      $result.resultCount = resultCount;
    }
    return $result;
  }
  RespScanStatus._() : super();
  factory RespScanStatus.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory RespScanStatus.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo('RespScanStatus', createEmptyInstance: create)
    ..aOB(1, 'scanFinished')
    ..a<$core.int>(2, 'resultCount', $pb.PbFieldType.OU3)
    ..hasRequiredFields = false;

  @$core.pragma('dart2js:noInline')
  static RespScanStatus create() => RespScanStatus._();
  RespScanStatus createEmptyInstance() => create();
  static $pb.PbList<RespScanStatus> createRepeated() => $pb.PbList<RespScanStatus>();
  @$core.pragma('dart2js:noInline')
  static RespScanStatus getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<RespScanStatus>(create);
  static RespScanStatus? _defaultInstance;

  $pb.BuilderInfo get info_ => _i;

  @$pb.TagNumber(1)
  $core.bool get scanFinished => $_getBF(0);
  @$pb.TagNumber(1)
  set scanFinished($core.bool v) { $_setBool(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasScanFinished() => $_has(0);
  @$pb.TagNumber(1)
  void clearScanFinished() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get resultCount => $_getIZ(1);
  @$pb.TagNumber(2)
  set resultCount($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasResultCount() => $_has(1);
  @$pb.TagNumber(2)
  void clearResultCount() => clearField(2);
}

class CmdScanResult extends $pb.GeneratedMessage {
  factory CmdScanResult({
    $core.int? startIndex,
    $core.int? count,
  }) {
    final $result = create();
    if (startIndex != null) {
      $result.startIndex = startIndex;
    }
    if (count != null) {
      $result.count = count;
    }
    return $result;
  }
  CmdScanResult._() : super();
  factory CmdScanResult.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory CmdScanResult.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo('CmdScanResult', createEmptyInstance: create)
    ..a<$core.int>(1, 'startIndex', $pb.PbFieldType.OU3)
    ..a<$core.int>(2, 'count', $pb.PbFieldType.OU3)
    ..hasRequiredFields = false;

  @$core.pragma('dart2js:noInline')
  static CmdScanResult create() => CmdScanResult._();
  CmdScanResult createEmptyInstance() => create();
  static $pb.PbList<CmdScanResult> createRepeated() => $pb.PbList<CmdScanResult>();
  @$core.pragma('dart2js:noInline')
  static CmdScanResult getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<CmdScanResult>(create);
  static CmdScanResult? _defaultInstance;

  $pb.BuilderInfo get info_ => _i;

  @$pb.TagNumber(1)
  $core.int get startIndex => $_getIZ(0);
  @$pb.TagNumber(1)
  set startIndex($core.int v) { $_setUnsignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasStartIndex() => $_has(0);
  @$pb.TagNumber(1)
  void clearStartIndex() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get count => $_getIZ(1);
  @$pb.TagNumber(2)
  set count($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasCount() => $_has(1);
  @$pb.TagNumber(2)
  void clearCount() => clearField(2);
}

class RespScanResult extends $pb.GeneratedMessage {
  factory RespScanResult({
    $core.Iterable<WiFiScanResult>? entries,
  }) {
    final $result = create();
    if (entries != null) {
      $result.entries.addAll(entries);
    }
    return $result;
  }
  RespScanResult._() : super();
  factory RespScanResult.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory RespScanResult.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo('RespScanResult', createEmptyInstance: create)
    ..pc<WiFiScanResult>(1, 'entries', $pb.PbFieldType.PM, subBuilder: WiFiScanResult.create)
    ..hasRequiredFields = false;

  @$core.pragma('dart2js:noInline')
  static RespScanResult create() => RespScanResult._();
  RespScanResult createEmptyInstance() => create();
  static $pb.PbList<RespScanResult> createRepeated() => $pb.PbList<RespScanResult>();
  @$core.pragma('dart2js:noInline')
  static RespScanResult getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<RespScanResult>(create);
  static RespScanResult? _defaultInstance;

  $pb.BuilderInfo get info_ => _i;

  @$pb.TagNumber(1)
  $core.List<WiFiScanResult> get entries => $_getList(0);
}

class WiFiScanResult extends $pb.GeneratedMessage {
  factory WiFiScanResult({
    $core.List<$core.int>? ssid,
    $core.int? channel,
    $core.int? rssi,
    WiFiAuthMode? auth,
  }) {
    final $result = create();
    if (ssid != null) {
      $result.ssid = ssid;
    }
    if (channel != null) {
      $result.channel = channel;
    }
    if (rssi != null) {
      $result.rssi = rssi;
    }
    if (auth != null) {
      $result.auth = auth;
    }
    return $result;
  }
  WiFiScanResult._() : super();
  factory WiFiScanResult.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory WiFiScanResult.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo('WiFiScanResult', createEmptyInstance: create)
    ..a<$core.List<$core.int>>(1, 'ssid', $pb.PbFieldType.OY)
    ..a<$core.int>(2, 'channel', $pb.PbFieldType.OU3)
    ..a<$core.int>(3, 'rssi', $pb.PbFieldType.O3)
    ..e<WiFiAuthMode>(4, 'auth', $pb.PbFieldType.OE, defaultOrMaker: WiFiAuthMode.Open, valueOf: WiFiAuthMode.valueOf, enumValues: WiFiAuthMode.values)
    ..hasRequiredFields = false;

  @$core.pragma('dart2js:noInline')
  static WiFiScanResult create() => WiFiScanResult._();
  WiFiScanResult createEmptyInstance() => create();
  static $pb.PbList<WiFiScanResult> createRepeated() => $pb.PbList<WiFiScanResult>();
  @$core.pragma('dart2js:noInline')
  static WiFiScanResult getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<WiFiScanResult>(create);
  static WiFiScanResult? _defaultInstance;

  $pb.BuilderInfo get info_ => _i;

  @$pb.TagNumber(1)
  $core.List<$core.int> get ssid => $_getN(0);
  @$pb.TagNumber(1)
  set ssid($core.List<$core.int> v) { $_setBytes(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasSsid() => $_has(0);
  @$pb.TagNumber(1)
  void clearSsid() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get channel => $_getIZ(1);
  @$pb.TagNumber(2)
  set channel($core.int v) { $_setUnsignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasChannel() => $_has(1);
  @$pb.TagNumber(2)
  void clearChannel() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get rssi => $_getIZ(2);
  @$pb.TagNumber(3)
  set rssi($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasRssi() => $_has(2);
  @$pb.TagNumber(3)
  void clearRssi() => clearField(3);

  @$pb.TagNumber(4)
  WiFiAuthMode get auth => $_getN(3);
  @$pb.TagNumber(4)
  set auth(WiFiAuthMode v) { setField(4, v); }
  @$pb.TagNumber(4)
  $core.bool hasAuth() => $_has(3);
  @$pb.TagNumber(4)
  void clearAuth() => clearField(4);
}

// Enums
class WiFiScanMsgType extends $pb.ProtobufEnum {
  static const WiFiScanMsgType TypeCmdScanStart = WiFiScanMsgType._(0, 'TypeCmdScanStart');
  static const WiFiScanMsgType TypeRespScanStart = WiFiScanMsgType._(1, 'TypeRespScanStart');
  static const WiFiScanMsgType TypeCmdScanStatus = WiFiScanMsgType._(2, 'TypeCmdScanStatus');
  static const WiFiScanMsgType TypeRespScanStatus = WiFiScanMsgType._(3, 'TypeRespScanStatus');
  static const WiFiScanMsgType TypeCmdScanResult = WiFiScanMsgType._(4, 'TypeCmdScanResult');
  static const WiFiScanMsgType TypeRespScanResult = WiFiScanMsgType._(5, 'TypeRespScanResult');

  static const $core.List<WiFiScanMsgType> values = <WiFiScanMsgType>[
    TypeCmdScanStart,
    TypeRespScanStart,
    TypeCmdScanStatus,
    TypeRespScanStatus,
    TypeCmdScanResult,
    TypeRespScanResult,
  ];

  static final $core.Map<$core.int, WiFiScanMsgType> _byValue = $pb.ProtobufEnum.initByValue(values);
  static WiFiScanMsgType? valueOf($core.int value) => _byValue[value];

  const WiFiScanMsgType._($core.int v, $core.String n) : super(v, n);
}

class WiFiAuthMode extends $pb.ProtobufEnum {
  static const WiFiAuthMode Open = WiFiAuthMode._(0, 'Open');
  static const WiFiAuthMode WEP = WiFiAuthMode._(1, 'WEP');
  static const WiFiAuthMode WPA_PSK = WiFiAuthMode._(2, 'WPA_PSK');
  static const WiFiAuthMode WPA2_PSK = WiFiAuthMode._(3, 'WPA2_PSK');
  static const WiFiAuthMode WPA_WPA2_PSK = WiFiAuthMode._(4, 'WPA_WPA2_PSK');
  static const WiFiAuthMode WPA2_ENTERPRISE = WiFiAuthMode._(5, 'WPA2_ENTERPRISE');
  static const WiFiAuthMode WPA3_PSK = WiFiAuthMode._(6, 'WPA3_PSK');
  static const WiFiAuthMode WPA2_WPA3_PSK = WiFiAuthMode._(7, 'WPA2_WPA3_PSK');

  static const $core.List<WiFiAuthMode> values = <WiFiAuthMode>[
    Open,
    WEP,
    WPA_PSK,
    WPA2_PSK,
    WPA_WPA2_PSK,
    WPA2_ENTERPRISE,
    WPA3_PSK,
    WPA2_WPA3_PSK,
  ];

  static final $core.Map<$core.int, WiFiAuthMode> _byValue = $pb.ProtobufEnum.initByValue(values);
  static WiFiAuthMode? valueOf($core.int value) => _byValue[value];

  const WiFiAuthMode._($core.int v, $core.String n) : super(v, n);
}

typedef Status = $0.Status;