// BLE Mesh Provisioning Protocol Implementation
import 'dart:async';
import 'dart:math';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'mesh_models.dart';
import 'mesh_security.dart';
import 'transport.dart';

class MeshProvisioner {
  final ProvTransport transport;
  final MeshProvisioningSecurity security;
  
  MeshProvisioningStatus _status = MeshProvisioningStatus.idle;
  MeshProvisioningCapabilities? _deviceCapabilities;
  final StreamController<MeshProvisioningStatus> _statusController = StreamController.broadcast();
  final StreamController<String> _logController = StreamController.broadcast();

  MeshProvisioner({
    required this.transport,
    required this.security,
  });

  Stream<MeshProvisioningStatus> get statusStream => _statusController.stream;
  Stream<String> get logStream => _logController.stream;
  MeshProvisioningStatus get status => _status;

  void _updateStatus(MeshProvisioningStatus newStatus) {
    _status = newStatus;
    _statusController.add(newStatus);
  }

  void _log(String message) {
    debugPrint('[MeshProvisioner] $message');
    _logController.add(message);
  }

  Future<MeshProvisioningResult> startProvisioning() async {
    try {
      _updateStatus(MeshProvisioningStatus.connecting);
      _log('Starting mesh provisioning...');

      // Step 1: Connect to device
      if (!await transport.connect()) {
        throw Exception('Failed to connect to device');
      }

      // Step 2: Send Provisioning Invite
      _updateStatus(MeshProvisioningStatus.inviting);
      await _sendProvisioningInvite();

      // Step 3: Receive and handle Capabilities
      _updateStatus(MeshProvisioningStatus.exchangingCapabilities);
      await _waitForCapabilities();

      // Step 4: Send Provisioning Start
      await _sendProvisioningStart();

      // Step 5: Exchange Public Keys
      _updateStatus(MeshProvisioningStatus.exchangingKeys);
      await _exchangePublicKeys();

      // Step 6: Authentication (No OOB)
      _updateStatus(MeshProvisioningStatus.authenticating);
      await _performAuthentication();

      // Step 7: Send Provisioning Data
      _updateStatus(MeshProvisioningStatus.sendingData);
      await _sendProvisioningData();

      // Step 8: Wait for Complete
      await _waitForComplete();

      _updateStatus(MeshProvisioningStatus.complete);
      _log('Mesh provisioning completed successfully!');

      return MeshProvisioningResult(
        status: MeshProvisioningStatus.complete,
        networkConfig: security.networkConfig,
      );

    } catch (e) {
      _updateStatus(MeshProvisioningStatus.failed);
      _log('Provisioning failed: $e');
      return MeshProvisioningResult(
        status: MeshProvisioningStatus.failed,
        errorMessage: e.toString(),
      );
    }
  }

  Future<void> _sendProvisioningInvite() async {
    _log('Sending provisioning invite...');
    
    // Provisioning Invite PDU: [Type (1 byte)] [AttentionDuration (1 byte)]
    final invitePDU = MeshProvisioningPDU(
      type: MeshProvisioningPDUType.invite,
      data: Uint8List.fromList([0x00]), // AttentionDuration: 0 seconds
    );

    await transport.sendReceive('mesh-prov', invitePDU.toBytes());
    _log('Provisioning invite sent');
  }

  Future<void> _waitForCapabilities() async {
    _log('Waiting for device capabilities...');
    
    // In a real implementation, this would be handled by the transport layer
    // For now, we'll simulate receiving capabilities
    await Future.delayed(const Duration(milliseconds: 500));
    
    // Simulate device capabilities
    _deviceCapabilities = MeshProvisioningCapabilities(
      numberOfElements: 1,
      supportedAlgorithms: 0x0001, // P-256 Elliptic Curve
      publicKeyType: 0x00, // No OOB Public Key
      staticOOBType: 0x00, // No Static OOB
      outputOOBSize: 0x00,
      outputOOBAction: 0x0000,
      inputOOBSize: 0x00,
      inputOOBAction: 0x0000,
    );
    
    _log('Received device capabilities: $_deviceCapabilities');
  }

  Future<void> _sendProvisioningStart() async {
    _log('Sending provisioning start...');
    
    // Provisioning Start PDU
    final startData = Uint8List.fromList([
      0x00, // Algorithm: P-256 Elliptic Curve
      0x00, // Public Key: No OOB Public Key
      0x00, // Auth Method: No OOB
      0x00, // Auth Action: No Action
      0x00, // Auth Size: 0
    ]);

    final startPDU = MeshProvisioningPDU(
      type: MeshProvisioningPDUType.start,
      data: startData,
    );

    await transport.sendReceive('mesh-prov', startPDU.toBytes());
    _log('Provisioning start sent');
  }

  Future<void> _exchangePublicKeys() async {
    _log('Exchanging public keys...');
    
    // Send our public key
    final publicKeyPDU = MeshProvisioningPDU(
      type: MeshProvisioningPDUType.publicKey,
      data: security.getProvisionerPublicKey(),
    );

    final response = await transport.sendReceive('mesh-prov', publicKeyPDU.toBytes());
    
    // Parse device public key response
    final responsePDU = MeshProvisioningPDU.fromBytes(response);
    if (responsePDU.type == MeshProvisioningPDUType.publicKey && responsePDU.data != null) {
      security.setDevicePublicKey(responsePDU.data!);
      security.calculateConfirmationKey();
      _log('Public key exchange completed');
    } else {
      throw Exception('Invalid public key response');
    }
  }

  Future<void> _performAuthentication() async {
    _log('Performing authentication...');
    
    // Set auth value for No OOB
    final authValue = security.generateAuthValue();
    security.setAuthValue(authValue);
    
    // Send confirmation
    final confirmation = security.getProvisionerConfirmation();
    final confirmationPDU = MeshProvisioningPDU(
      type: MeshProvisioningPDUType.confirmation,
      data: confirmation,
    );

    final confirmResponse = await transport.sendReceive('mesh-prov', confirmationPDU.toBytes());
    
    // Parse device confirmation
    final confirmResponsePDU = MeshProvisioningPDU.fromBytes(confirmResponse);
    if (confirmResponsePDU.type != MeshProvisioningPDUType.confirmation) {
      throw Exception('Expected confirmation response');
    }

    // Send random
    final randomPDU = MeshProvisioningPDU(
      type: MeshProvisioningPDUType.random,
      data: security.getProvisionerRandom(),
    );

    final randomResponse = await transport.sendReceive('mesh-prov', randomPDU.toBytes());
    
    // Parse device random and verify
    final randomResponsePDU = MeshProvisioningPDU.fromBytes(randomResponse);
    if (randomResponsePDU.type == MeshProvisioningPDUType.random && randomResponsePDU.data != null) {
      security.setDeviceRandom(randomResponsePDU.data!);
      
      // Verify device confirmation
      if (!security.verifyConfirmation(confirmResponsePDU.data!, randomResponsePDU.data!, authValue)) {
        throw Exception('Confirmation verification failed');
      }
      
      // Calculate session and device keys
      security.calculateSessionKey();
      security.calculateDeviceKey();
      
      _log('Authentication completed successfully');
    } else {
      throw Exception('Invalid random response');
    }
  }

  Future<void> _sendProvisioningData() async {
    _log('Sending provisioning data...');
    
    // Create provisioning data
    final provisioningData = MeshProvisioningData(
      networkKey: security.networkConfig.networkKey,
      keyIndex: security.networkConfig.networkKeyIndex,
      flags: security.networkConfig.flags,
      ivIndex: security.networkConfig.ivIndex,
      unicastAddress: security.networkConfig.unicastAddress,
    );

    // Encrypt provisioning data
    final encryptedData = security.encryptProvisioningData(provisioningData);
    
    final dataPDU = MeshProvisioningPDU(
      type: MeshProvisioningPDUType.data,
      data: encryptedData,
    );

    await transport.sendReceive('mesh-prov', dataPDU.toBytes());
    _log('Provisioning data sent');
  }

  Future<void> _waitForComplete() async {
    _log('Waiting for provisioning complete...');
    
    // In a real implementation, we would wait for the complete PDU
    // For now, simulate a successful completion
    await Future.delayed(const Duration(milliseconds: 1000));
    
    _log('Received provisioning complete');
  }

  void dispose() {
    _statusController.close();
    _logController.close();
    transport.disconnect();
  }
}
